@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                   🎨 CLONIE ICON STATUS 🎨                   ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 Checking icon files in assets folder...
echo.

cd assets

if exist "clonie_icon.png" (
    echo ✅ clonie_icon.png - Your custom icon source
) else (
    echo ❌ clonie_icon.png - MISSING! This is your custom icon file
)

if exist "icon.png" (
    echo ✅ icon.png - Application icon (copied from clonie_icon.png)
) else (
    echo ❌ icon.png - MISSING! Needed for Electron app
)

if exist "icon.ico" (
    echo ✅ icon.ico - Windows installer icon
) else (
    echo ❌ icon.ico - MISSING! Needed for Windows installer
)

echo.
echo 📋 Icon Usage Summary:
echo    • clonie_icon.png → Your original custom icon
echo    • icon.png → Used by Electron app window and system tray
echo    • icon.ico → Used by Windows installer and shortcuts
echo.

if exist "clonie_icon.png" if exist "icon.png" if exist "icon.ico" (
    echo 🎉 ALL ICONS READY!
    echo    Your custom Clonie icon will appear in:
    echo    ✓ Application window title bar
    echo    ✓ System tray
    echo    ✓ Desktop shortcut
    echo    ✓ Start Menu entry
    echo    ✓ Windows installer
    echo    ✓ Programs & Features list
    echo.
    echo 🚀 Ready to build installer with custom icons!
) else (
    echo ⚠️  SETUP NEEDED
    echo.
    if not exist "clonie_icon.png" (
        echo    1. Add your clonie_icon.png to the assets folder
    )
    if not exist "icon.png" (
        echo    2. Run: assets\setup-icons-simple.bat
    )
    if not exist "icon.ico" (
        echo    3. For best results, convert PNG to ICO format online
    )
)

echo.
echo 💡 Pro Tip: For the best Windows installer experience,
echo    convert your clonie_icon.png to ICO format using:
echo    https://convertio.co/png-ico/
echo    Then replace icon.ico with the converted file.
echo.

pause
