# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent.parent / 'backend'
sys.path.insert(0, str(backend_dir))

block_cipher = None

# Collect all TTS model files and dependencies
def collect_tts_data():
    """Collect TTS model files and data"""
    datas = []
    
    # Try to find TTS installation
    try:
        import TTS
        tts_path = Path(TTS.__file__).parent
        
        # Include model configs and other data files
        for pattern in ['**/*.json', '**/*.pth', '**/*.txt']:
            for file in tts_path.rglob(pattern):
                if file.is_file():
                    rel_path = file.relative_to(tts_path.parent)
                    datas.append((str(file), str(rel_path.parent)))
    except ImportError:
        print("Warning: TTS not found, models will be downloaded at runtime")
    
    return datas

# Hidden imports for TTS and related packages
hidden_imports = [
    'TTS',
    'TTS.api',
    'TTS.tts',
    'TTS.tts.configs',
    'TTS.tts.configs.xtts_config',
    'TTS.tts.models',
    'TTS.tts.models.xtts',
    'torch',
    'torchaudio',
    'numpy',
    'scipy',
    'librosa',
    'soundfile',
    'fastapi',
    'uvicorn',
    'pydantic',
    'multipart',
    'starlette',
    'anyio',
    'sniffio',
    'h11',
    'httptools',
    'websockets',
    'python_multipart',
    'email_validator',
    'jinja2',
    'aiofiles',
    'itsdangerous'
]

a = Analysis(
    [str(backend_dir / 'main.py')],
    pathex=[str(backend_dir)],
    binaries=[],
    datas=collect_tts_data(),
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'PIL',
        'cv2',
        'sklearn',
        'pandas',
        'jupyter',
        'notebook',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Filter out unnecessary files to reduce size
def filter_binaries(binaries):
    """Filter out unnecessary binary files"""
    filtered = []
    exclude_patterns = [
        'api-ms-win',
        'ucrtbase',
        'msvcp',
        'vcruntime',
        'Qt5',
        'libssl',
        'libcrypto'
    ]
    
    for binary in binaries:
        name = binary[0].lower()
        if not any(pattern in name for pattern in exclude_patterns):
            filtered.append(binary)
    
    return filtered

a.binaries = filter_binaries(a.binaries)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='clonie-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
