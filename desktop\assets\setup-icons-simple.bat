@echo off
echo.
echo Setting up Clonie custom icons...
echo.

cd /d "%~dp0"

if exist "clonie_icon.png" (
    echo Found clonie_icon.png
    
    echo Copying as icon.png...
    copy "clonie_icon.png" "icon.png" >nul
    
    echo Copying as icon.ico...
    copy "clonie_icon.png" "icon.ico" >nul
    
    echo.
    echo Custom icon setup complete!
    echo Your Clonie app will use the custom icon.
    echo.
    echo Note: For best results, convert clonie_icon.png to ICO format
    echo using an online converter like https://convertio.co/png-ico/
    echo and replace icon.ico with the converted file.
    
) else (
    echo clonie_icon.png not found!
    echo Please make sure the file is in the assets folder.
)

echo.
pause
