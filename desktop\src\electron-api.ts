import axios from 'axios';

// Extend the Window interface to include electronAPI
declare global {
  interface Window {
    electronAPI?: {
      getBackendPort: () => Promise<number>;
      showSaveDialog: (options: any) => Promise<any>;
      onMenuOpenFile: (callback: () => void) => void;
      platform: string;
      isElectron: boolean;
    };
  }
}

let API_BASE_URL = 'http://localhost:8000';
let api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minutes for voice processing
});

// Initialize API with dynamic port for Electron
export async function initializeAPI() {
  if (window.electronAPI) {
    try {
      const port = await window.electronAPI.getBackendPort();
      API_BASE_URL = `http://localhost:${port}`;
      api = axios.create({
        baseURL: API_BASE_URL,
        timeout: 300000,
      });
      console.log(`Electron API initialized with backend port: ${port}`);
    } catch (error) {
      console.error('Failed to get backend port from Electron:', error);
    }
  }
}

export interface VoiceCloneResponse {
  success: boolean;
  message: string;
  voice_info?: string;
  sample_audio_id?: string;
}

export interface TTSResponse {
  success: boolean;
  message: string;
  audio_id?: string;
}

export interface TTSRequest {
  text: string;
  quality_mode: 'high' | 'standard';
  remove_silence: boolean;
}

export class ElectronVoiceCloningAPI {
  static async cloneVoice(
    audioFile: File,
    enhancementLevel: 'none' | 'minimal' | 'gentle' = 'none'
  ): Promise<VoiceCloneResponse> {
    const formData = new FormData();
    formData.append('audio_file', audioFile);
    formData.append('enhancement_level', enhancementLevel);

    const response = await api.post<VoiceCloneResponse>('/clone-voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  static async generateTTS(request: TTSRequest): Promise<TTSResponse> {
    const response = await api.post<TTSResponse>('/generate-tts', request);
    return response.data;
  }

  static getAudioUrl(audioId: string): string {
    return `${API_BASE_URL}/audio/${audioId}`;
  }

  static async deleteAudio(audioId: string): Promise<void> {
    await api.delete(`/audio/${audioId}`);
  }

  static async healthCheck(): Promise<{ status: string; voice_system_ready: boolean }> {
    const response = await api.get('/health');
    return response.data;
  }

  // Electron-specific methods
  static async saveAudioFile(audioId: string, defaultName: string): Promise<string | null> {
    if (!window.electronAPI) {
      // Fallback to browser download
      const url = this.getAudioUrl(audioId);
      const link = document.createElement('a');
      link.href = url;
      link.download = defaultName;
      link.click();
      return null;
    }

    try {
      const result = await window.electronAPI.showSaveDialog({
        title: 'Save Audio File',
        defaultPath: defaultName,
        filters: [
          { name: 'Audio Files', extensions: ['wav'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        // Download the file to the selected path
        const response = await api.get(`/audio/${audioId}`, {
          responseType: 'blob'
        });

        // Convert blob to buffer and save (this would need additional implementation)
        // For now, we'll use the browser download as fallback
        const url = this.getAudioUrl(audioId);
        const link = document.createElement('a');
        link.href = url;
        link.download = result.filePath.split(/[\\/]/).pop() || defaultName;
        link.click();

        return result.filePath;
      }
    } catch (error) {
      console.error('Error saving file:', error);
    }

    return null;
  }

  static isElectron(): boolean {
    return window.electronAPI?.isElectron || false;
  }

  static getPlatform(): string {
    return window.electronAPI?.platform || 'web';
  }
}

export default ElectronVoiceCloningAPI;
