# Clonie Desktop Application

Windows desktop version of the high-fidelity voice cloning application, built with Electron.

## 🎯 **Quick Start - Create Windows Installer**

**Want to create a double-clickable installer?** Just run:

```bash
BUILD-INSTALLER.bat
```

This creates `Clonie-Setup-1.0.0.exe` that users can double-click to install!

## Features

### 🖥️ **Native Desktop Experience**
- Standalone Windows executable
- System tray integration
- Native file dialogs and drag-and-drop
- Window management controls
- Offline operation (no browser required)

### 🎙️ **Same Voice Cloning Quality**
- Identical functionality to web version
- XTTS v2 model integration
- High-fidelity audio processing
- Multiple enhancement levels
- Professional-grade output

### ⚡ **Desktop Optimizations**
- Bundled Python backend (no separate installation)
- Automatic backend startup/shutdown
- Optimized resource usage
- Fast startup times

## Quick Start

### Option 1: Use Pre-built Installer (Recommended)
1. Download `Clonie-Setup-1.0.0.exe` from releases
2. Run the installer
3. Launch Clonie from Start Menu or Desktop
4. Wait for initial model download (~1GB, first run only)
5. Start cloning voices!

### Option 2: Build from Source

#### Prerequisites
- Node.js 18+ 
- Python 3.8+
- Git

#### Build Steps
```bash
# Clone the repository
git clone <repository-url>
cd clonie/desktop

# Run the complete build
scripts\build-all.bat
```

The installer will be created in `desktop\release\`

## Development

### Development Mode
```bash
cd desktop
scripts\dev.bat
```

This runs Electron with the Python backend in development mode.

### Project Structure
```
desktop/
├── src/
│   ├── main.js           # Electron main process
│   ├── preload.js        # Secure IPC bridge
│   ├── ElectronApp.tsx   # React app for desktop
│   └── electron-api.ts   # Desktop API wrapper
├── scripts/
│   ├── build-all.bat     # Complete build script
│   └── dev.bat           # Development script
├── backend-build/
│   └── main.spec         # PyInstaller configuration
└── package.json          # Electron configuration
```

## Desktop-Specific Features

### System Tray
- Minimize to system tray
- Right-click context menu
- Double-click to show/hide
- Backend status indicator

### File Handling
- Drag and drop audio files anywhere in the app
- Native file open/save dialogs
- File association (future feature)

### Window Management
- Minimize, maximize, close controls
- Remember window size and position
- Multi-monitor support

### Keyboard Shortcuts
- `Ctrl+O` - Open audio file
- `Ctrl+Q` - Quit application
- `F11` - Toggle fullscreen
- `F12` - Toggle developer tools (dev mode)

## Technical Details

### Backend Integration
- Python backend bundled with PyInstaller
- Automatic port detection and assignment
- Graceful startup/shutdown handling
- Error recovery and restart capabilities

### Security
- Context isolation enabled
- Node integration disabled
- Secure IPC communication
- No remote module access

### Performance
- ~200MB installed size
- ~2-4GB RAM usage (model loading)
- GPU acceleration when available
- Optimized for Windows 10/11

## Troubleshooting

### Common Issues

**App won't start:**
- Check Windows Defender/antivirus settings
- Run as administrator if needed
- Check system requirements (Windows 10+)

**Backend errors:**
- First run downloads model (~1GB) - be patient
- Ensure sufficient disk space (5GB recommended)
- Check firewall settings for localhost communication

**Audio issues:**
- Check Windows audio permissions
- Verify audio device is working
- Try different enhancement levels

### Logs and Debugging
- Console logs available in development mode (`F12`)
- Backend logs shown in development console
- Check Windows Event Viewer for system-level issues

## Building for Distribution

### Complete Build Process
1. **Frontend Build**: React app compiled to static files
2. **Backend Build**: Python app bundled to executable
3. **Electron Build**: Desktop app with installer
4. **Code Signing**: (Optional) Sign executable for trust

### Build Requirements
- Windows 10+ (for building Windows app)
- 8GB+ RAM (for PyInstaller)
- 10GB+ free disk space
- Fast internet (for model downloads during build)

### Installer Features
- NSIS-based Windows installer
- Desktop and Start Menu shortcuts
- Uninstaller included
- User-selectable install directory
- Automatic updates (future feature)

## Version History

### v1.0.0
- Initial desktop release
- Full feature parity with web version
- System tray integration
- Native file handling
- Windows installer

## Support

For issues specific to the desktop version:
1. Check this README
2. Review console logs (`F12` in dev mode)
3. Check Windows Event Viewer
4. Report issues with system information

## License

Same license as the main Clonie project.
