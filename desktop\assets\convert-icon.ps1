# PowerShell script to convert PNG to ICO
Write-Host "🎨 Converting Clonie icon from PNG to ICO format..."

$currentDir = Get-Location
$pngPath = Join-Path $currentDir "clonie_icon.png"
$icoPath = Join-Path $currentDir "icon.ico"
$iconPngPath = Join-Path $currentDir "icon.png"

# Check if source file exists
if (Test-Path $pngPath) {
    Write-Host "Found clonie_icon.png"
    
    # Copy as icon.png
    Copy-Item $pngPath $iconPngPath -Force
    Write-Host "✅ Created icon.png"
    
    try {
        # Load System.Drawing assembly
        Add-Type -AssemblyName System.Drawing
        
        # Load the PNG image
        $png = [System.Drawing.Image]::FromFile($pngPath)
        
        # Create icon from image
        $icon = [System.Drawing.Icon]::FromHandle(([System.Drawing.Bitmap]$png).GetHicon())
        
        # Save as ICO file
        $fileStream = [System.IO.File]::Create($icoPath)
        $icon.Save($fileStream)
        $fileStream.Close()
        
        # Clean up
        $icon.Dispose()
        $png.Dispose()
        
        Write-Host "✅ Successfully created icon.ico"
        Write-Host ""
        Write-Host "🎯 Icon setup complete! Your custom Clonie icon is ready for:"
        Write-Host "   • Application window"
        Write-Host "   • System tray"
        Write-Host "   • Desktop shortcut"
        Write-Host "   • Start Menu entry"
        Write-Host "   • Windows installer"
        
    } catch {
        Write-Host "⚠️  PowerShell conversion failed: $($_.Exception.Message)"
        Write-Host ""
        Write-Host "📝 Alternative options:"
        Write-Host "   1. Use online converter: https://convertio.co/png-ico/"
        Write-Host "   2. Install ImageMagick and run: magick clonie_icon.png icon.ico"
        Write-Host ""
        Write-Host "   For now, copying PNG as ICO (may work)..."
        Copy-Item $pngPath $icoPath -Force
        Write-Host "✅ Created placeholder icon.ico"
    }
    
} else {
    Write-Host "❌ clonie_icon.png not found in current directory"
    Write-Host "📁 Current directory: $currentDir"
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
