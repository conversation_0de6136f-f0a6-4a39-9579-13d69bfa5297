import React from 'react';
import { createRoot } from 'react-dom/client';
import ElectronApp from './ElectronApp';
import '../../frontend/src/style.css';

const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <ElectronApp />
  </React.StrictMode>
);

console.log('Clonie Desktop App loaded');
