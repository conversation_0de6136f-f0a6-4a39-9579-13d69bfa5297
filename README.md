# Clonie - High-Fidelity Voice Cloning Web App

A modern web application for high-fidelity voice cloning and text-to-speech synthesis, built with React frontend and Python backend.

## Project Structure

```
clonie/
├── frontend/          # React + Vite frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Python FastAPI backend
│   ├── voice_cloning_tts.py  # Original implementation
│   ├── main.py        # FastAPI server
│   └── requirements.txt
└── README.md
```

## Features

- **High-Fidelity Voice Cloning**: Uses XTTS v2 model for natural voice synthesis
- **Minimal Processing**: Preserves original voice quality with optional gentle enhancement
- **Modern Web Interface**: React-based UI with responsive design
- **Real-time Audio Processing**: Upload, clone, and generate speech seamlessly
- **Multiple Quality Modes**: Choose between high and standard quality synthesis
- **Enhancement Levels**: None, minimal, or gentle audio processing options

## Technology Stack

### Frontend
- React 18 with TypeScript
- Vite for fast development and building
- Modern CSS with responsive design
- Audio visualization and playback controls

### Backend
- Python with FastAPI
- PyTorch and TTS library for voice synthesis
- High-quality audio processing with librosa
- RESTful API design

## Getting Started

### Quick Start (Recommended)
```bash
# On Windows
start-clonie.bat

# On Linux/Mac
./start-clonie.sh
```

### Manual Setup

#### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python main.py
```
Backend will be available at: http://localhost:8000

#### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```
Frontend will be available at: http://localhost:5173

### First Run Notes
- First startup downloads the XTTS v2 model (~1GB)
- Model initialization takes 2-5 minutes
- Requires ~2-4GB RAM for model loading
- GPU acceleration used automatically if CUDA available

## Original Features Preserved

All functionality from the original Gradio application has been maintained:
- Voice sample upload and processing
- Enhancement level selection (none/minimal/gentle)
- Quality mode selection (high/standard)
- Text-to-speech generation
- Audio download capabilities
- Real-time status updates

## Development Status

This is a conversion of the original Python/Gradio application to a modern web stack while preserving all core functionality and focusing on improved user experience.
