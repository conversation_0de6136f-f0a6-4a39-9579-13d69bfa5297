# Clonie Desktop - Installation Guide

## Quick Installation

### For End Users

1. **Download the Installer**
   - Get `C<PERSON>ie-Setup-1.0.0.exe` from the releases
   - File size: ~200MB

2. **Run the Installer**
   - Double-click the installer
   - Follow the installation wizard
   - Choose installation directory (default: `C:\Program Files\C<PERSON>ie`)
   - Create desktop shortcut (recommended)

3. **First Launch**
   - Launch from Desktop shortcut or Start Menu
   - **Important**: First startup downloads the AI model (~1GB)
   - This may take 2-5 minutes depending on internet speed
   - Subsequent launches are much faster

4. **Start Using**
   - Upload a voice sample (10-30 seconds recommended)
   - Generate speech from text
   - Download your cloned voice audio

## For Developers

### Prerequisites
```bash
# Required software
- Node.js 18+ (https://nodejs.org/)
- Python 3.8+ (https://python.org/)
- Git (https://git-scm.com/)
- Windows 10+ (for building Windows app)
```

### Development Setup
```bash
# 1. Clone the repository
git clone <your-repo-url>
cd clonie

# 2. Install backend dependencies
cd backend
pip install -r requirements.txt

# 3. Install frontend dependencies  
cd ../frontend
npm install

# 4. Install desktop dependencies
cd ../desktop
npm install

# 5. Test in development mode
scripts\dev.bat
```

### Building the Desktop App

#### Quick Build (Development)
```bash
cd desktop
scripts\dev.bat
```

#### Full Production Build
```bash
cd desktop
scripts\build-all.bat
```

This creates:
- `desktop\release\Clonie-Setup-1.0.0.exe` - Windows installer
- `desktop\release\win-unpacked\` - Unpacked application files

### Build Process Details

The build process involves several steps:

1. **Frontend Build**
   - React app compiled with Vite
   - Static files generated in `frontend/dist`
   - Copied to `desktop/dist`

2. **Backend Build**
   - Python app bundled with PyInstaller
   - Creates standalone executable
   - Includes all dependencies and AI models
   - Output: `desktop/backend-dist/main.exe`

3. **Electron Build**
   - Packages everything into desktop app
   - Creates Windows installer with NSIS
   - Includes auto-updater configuration
   - Code signing (if certificates available)

## System Requirements

### Minimum Requirements
- **OS**: Windows 10 (64-bit)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 5GB free space
- **Internet**: Required for initial model download

### Recommended Requirements
- **OS**: Windows 11 (64-bit)
- **RAM**: 8GB or more
- **Storage**: 10GB free space (SSD preferred)
- **GPU**: CUDA-compatible GPU (optional, for faster processing)
- **Internet**: Broadband for model downloads

## Troubleshooting Installation

### Common Installation Issues

**"Windows protected your PC" warning:**
- Click "More info" → "Run anyway"
- This happens because the app isn't code-signed yet
- Future versions will be properly signed

**Installer fails to run:**
- Run as Administrator
- Temporarily disable antivirus
- Check available disk space (5GB minimum)

**Installation hangs:**
- Check internet connection (downloads AI model)
- Wait patiently - first download can take 5+ minutes
- Check Windows Defender isn't blocking downloads

### First Launch Issues

**App starts but shows "Backend not available":**
- Wait 2-5 minutes for model initialization
- Check Windows Firewall isn't blocking localhost
- Restart the application

**"Failed to download model" error:**
- Check internet connection
- Try running as Administrator
- Manually download model (see Advanced Setup)

**High memory usage:**
- Normal behavior - AI model requires 2-4GB RAM
- Close other applications if needed
- Consider upgrading RAM for better performance

### Advanced Setup

**Manual Model Download:**
If automatic download fails, you can manually download the XTTS v2 model:

1. Download from: https://huggingface.co/coqui/XTTS-v2
2. Extract to: `%USERPROFILE%\.cache\tts\tts_models--multilingual--multi-dataset--xtts_v2`
3. Restart Clonie

**Custom Installation Directory:**
- Choose during installation
- Ensure directory has write permissions
- Avoid paths with special characters

**Portable Installation:**
- Extract from installer using 7-Zip
- Copy to USB drive or network location
- May require manual model setup

## Uninstallation

### Standard Uninstall
1. Go to Windows Settings → Apps
2. Find "Clonie" in the list
3. Click "Uninstall"
4. Follow the uninstaller prompts

### Manual Cleanup (if needed)
```bash
# Remove application data
rmdir /s "%APPDATA%\Clonie"

# Remove cached models
rmdir /s "%USERPROFILE%\.cache\tts"

# Remove desktop shortcuts
del "%USERPROFILE%\Desktop\Clonie.lnk"
del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Clonie.lnk"
```

## Security Notes

- The app runs locally - no data sent to external servers
- Voice samples and generated audio stay on your computer
- Internet only used for initial AI model download
- All processing happens offline after setup

## Performance Tips

- **First run**: Be patient during model download
- **SSD recommended**: Faster model loading
- **Close other apps**: More RAM available for AI model
- **GPU acceleration**: Automatically used if CUDA available
- **Regular restarts**: Clears memory leaks in long sessions

## Getting Help

1. **Check this guide** for common issues
2. **Review logs** in development mode (`F12`)
3. **Check system requirements** are met
4. **Try reinstalling** if problems persist
5. **Report bugs** with system information

## Version Information

- **Current Version**: 1.0.0
- **Electron Version**: 28.x
- **Python Version**: 3.8+
- **AI Model**: XTTS v2
- **Supported OS**: Windows 10/11 (64-bit)
