{"ALL_COMPILER_OPTIONS_6917": "TODAS AS OPÇÕES DO COMPILADOR", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Um modificador '{0}' não pode ser usado com uma declaração de importação.", "A_0_parameter_must_be_the_first_parameter_2680": "Um parâmetro '{0}' deve ser o primeiro parâmetro.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "Uma marca JSDoc '@template' pode não seguir uma marca '@typedef', '@callback' ou '@overload'", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Um comentário de JSDoc '@typedef' não pode conter várias marcas '@type'.", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "Um literal 'bigint' não pode ser usado como um nome de propriedade.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Um literal de bigint não pode usar notação exponencial.", "A_bigint_literal_must_be_an_integer_1353": "Um literal de bigint deve ser um inteiro.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Um parâmetro de padrão de associação não pode ser opcional em uma assinatura de implementação.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Uma instrução 'break' só pode ser usada em uma iteração de circunscrição ou instrução switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Uma instrução 'break' só pode saltar para um rótulo de uma instrução de circunscrição.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "Uma classe de caracteres não deve conter um pontuador duplo reservado. Você quis escapar com uma barra invertida?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "O intervalo de uma classe de caracteres não deve ser limitado por outra classe de caracteres.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Uma classe pode implementar apenas um identificador/nome qualificado com argumentos de tipo opcionais.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Uma classe pode implementar somente um tipo de objeto ou interseção de tipos de objeto com membros estaticamente conhecidos.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "Uma classe não pode estender um tipo primitivo como '{0}'. As classes só podem estender valores construtíveis.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "Uma classe não pode implementar um tipo primitivo como '{0}'. Ela só pode implementar outros tipos de objetos nomeados.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Uma declaração de classe sem o modificador 'default' deve ter um nome.", "A_class_member_cannot_have_the_0_keyword_1248": "Um membro de classe não pode ter a palavra-chave '{0}'.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Uma expressão de vírgula não é permitida em um nome de propriedade calculado.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Um nome de propriedade calculado não pode fazer referência a um parâmetro de tipo no seu tipo recipiente.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Um nome de propriedade computado em uma declaração de propriedade de classe precisa ter um tipo literal simples ou um tipo 'símbolo exclusivo'.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Um nome de propriedade computado em uma sobrecarga do método deve se referir a uma expressão cujo tipo é um tipo literal ou um 'unique symbol'.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Um nome de propriedade computado em um tipo literal deve se referir a uma expressão cujo tipo é um tipo literal ou um 'unique symbol'.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Um nome de propriedade computado em um contexto de ambiente deve se referir a uma expressão cujo tipo é um tipo literal ou um 'unique symbol'.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Um nome de propriedade computado em uma interface deve se referir a uma expressão cujo tipo é um tipo literal ou um 'unique symbol'.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Um nome de propriedade calculado deve ser do tipo 'string', 'number', 'symbol' ou 'any'.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "As declarações 'const' só podem ser aplicadas a referências a membros de enumeração ou literais de cadeia de caracteres, número, bool<PERSON>, matriz ou objeto.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Um membro const enum só pode ser acessado usando um literal de cadeia de caracteres.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Um inicializador 'const' em um contexto de ambiente deve ser uma cadeia de caracteres ou um literal numérico ou uma referência de enumeração literal.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Um construtor não pode conter uma chamada 'super' quando sua classe estende 'null'.", "A_constructor_cannot_have_a_this_parameter_2681": "Um construtor não pode ter um parâmetro 'this'.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Uma instrução 'continue' só pode ser usada em uma instrução de iteração de circunscrição.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Uma instrução 'continue' só pode saltar para um rótulo de uma instrução de iteração de circunscrição.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "Um arquivo de declaração não pode ser importado sem 'import type'. Você quis importar um arquivo de implementação '{0}'?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Um modificador 'declare' não pode ser usado em um contexto de ambiente.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Um decorador pode decorar somente uma implementação de método, não uma sobrecarga.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Uma cláusula 'default' não pode aparecer mais de uma vez em uma instrução 'switch'.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Uma exportação padrão só pode ser usada em um módulo do estilo ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Uma exportação padrão deve estar no nível superior de uma declaração de arquivo ou módulo.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Uma declaração de atribuição definitiva '!' não é permitida neste contexto.", "A_destructuring_declaration_must_have_an_initializer_1182": "Uma declaração de desestruturação deve ter um inicializador.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "Uma chamada de importação dinâmica no ES5 requer o construtor 'Promise'.  Verifique se você tem uma declaração para o construtor 'Promise' ou inclua 'ES2015' na sua opção '--lib'.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Uma chamada de importação dinâmica retorna um 'Promise'. Verifique se você tem uma declaração para 'Promise' ou inclua 'ES2015' na sua opção '--lib'.", "A_file_cannot_have_a_reference_to_itself_1006": "Um arquivo não pode fazer referência a si mesmo.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Uma função que retorna 'never' não pode ter um ponto de extremidade acessível.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Uma função chamada com a palavra-chave 'new' não pode ter um tipo 'this' que seja 'void'.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "Uma função cujo tipo declarado não seja 'undefined', 'void' nem 'any' deve retornar um valor.", "A_generator_cannot_have_a_void_type_annotation_2505": "O gerador não pode ter uma anotação de tipo 'void'.", "A_get_accessor_cannot_have_parameters_1054": "Um acessador 'get' não pode ter parâmetros.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Um acessador get precisa ser pelo menos tão acessível quanto o setter", "A_get_accessor_must_return_a_value_2378": "Um acessador 'get' deve retornar um valor.", "A_label_is_not_allowed_here_1344": "Um rótulo não é permitido aqui.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Um elemento de tupla rotulado é declarado como opcional com um ponto de interrogação depois do nome e antes de dois-pontos, em vez de depois do tipo.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Um elemento de tupla rotulado foi declarado como Rest com um '...' antes do nome, em vez de antes do tipo.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Um tipo mapeado não pode declarar propriedades ou métodos.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "O inicializador de um membro em uma declaração de enumeração não pode referenciar membros declarados depois dele, inclusive membros definidos em outras enumerações.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Uma classe mixin deve ter um construtor um único parâmetro rest do tipo 'any[]'.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Uma classe mixin que se estende de uma variável de tipo contendo uma assinatura de constructo abstrata também precisa ser declarada como 'abstract'.", "A_module_cannot_have_multiple_default_exports_2528": "Um módulo não pode ter várias exportações padrão.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Uma declaração de namespace não pode estar em um arquivo diferente de uma classe ou função com a qual ela é mesclada.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Uma declaração de namespace não pode estar localizada antes de uma classe ou função com a qual ela é mesclada.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Uma declaração de namespace só é permitida no nível superior de um namespace ou módulo.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "Uma declaração \"namespace\" não deve ser declarada usando a palavra-chave \"module\". Use a palavra-chave \"namespace\".", "A_non_dry_build_would_build_project_0_6357": "Um build não -dry criaria o projeto '{0}'", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Um build não -dry excluiria os seguintes arquivos: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Um build não -dry atualizaria carimbos de data/hora para a saída do projeto '{0}'", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Um inicializador de parâmetro só é permitido em uma implementação de função ou de construtor.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Uma propriedade de parâmetro não pode ser declarada usando um parâmetro rest.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Uma propriedade de parâmetro somente é permitida em uma implementação de construtor.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Uma propriedade de parâmetro pode não ser declarada usando um padrão de associação.", "A_promise_must_have_a_then_method_1059": "Uma promessa deve ter um método 'then'.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Uma propriedade de uma classe cujo tipo é um tipo de 'unique symbol' deve ser 'static' e 'readonly'.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Uma propriedade de uma interface ou tipo literal cujo tipo é um tipo de 'unique symbol' deve ser 'readonly'.", "A_required_element_cannot_follow_an_optional_element_1257": "Um elemento obrigatório não pode seguir um elemento opcional.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Um parâmetro obrigatório não pode seguir um parâmetro opcional.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Um elemento rest não pode conter um padrão de associação.", "A_rest_element_cannot_follow_another_rest_element_1265": "Um elemento REST não pode seguir outro elemento REST.", "A_rest_element_cannot_have_a_property_name_2566": "Um elemento rest não pode ter um nome de propriedade.", "A_rest_element_cannot_have_an_initializer_1186": "Um elemento rest não pode ter um inicializador.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Um elemento rest deve ser o último em um padrão de desestruturação.", "A_rest_element_type_must_be_an_array_type_2574": "Um elemento rest deve ser um tipo de matriz.", "A_rest_parameter_cannot_be_optional_1047": "Um parâmetro rest não pode ser opcional.", "A_rest_parameter_cannot_have_an_initializer_1048": "Um parâmetro rest não pode ter um inicializador.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Um parâmetro rest deve ser o último em uma lista de parâmetros.", "A_rest_parameter_must_be_of_an_array_type_2370": "Um parâmetro rest deve ser de um tipo de matriz.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Um padrão de associação ou o parâmetro rest não pode ter uma vírgula à direita.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Uma instrução 'return' s<PERSON> pode ser usada dentro de um corpo de função.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Não é possível usar uma instrução “return” dentro de um bloco estático de classe.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Uma série de entradas que o remapeamento importa para pesquisar locais relativos a 'baseUrl'.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Um acessador 'set' não pode ter uma anotação de tipo de retorno.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Um acessador 'set' não pode ter um parâmetro opcional.", "A_set_accessor_cannot_have_rest_parameter_1053": "Um acessador 'set' não pode ter um parâmetro rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Um acessador 'set' deve ter exatamente um parâmetro.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Um parâmetro de acessador 'set' não pode ter um inicializador.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Um argumento de espalhamento deve ter um tipo de tupla ou ser passado para um parâmetro Rest.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Uma chamada 'super' deve ser uma instrução de nível raiz dentro de um construtor de uma classe derivada que contém propriedades inicializadas, propriedades de parâmetro ou identificadores privados.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Uma chamada 'super' deve ser a primeira instrução no construtor a se referir a 'super' ou 'this' quando uma classe derivada contém propriedades inicializadas, propriedades de parâmetro ou identificadores privados.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Uma proteção de tipo baseado em 'this não é compatível com uma proteção de tipo baseado em parâmetro.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Um tipo 'this' está disponível somente em um membro não estático de uma classe ou interface.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "Um modificador 'export' de nível superior não pode ser usado em declarações de valor em um módulo CommonJS quando 'verbatimModuleSyntax' estiver habilitado.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Um arquivo 'tsconfig.json' já está definido em: '{0}'.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Um membro de tupla não pode ser opcional e rest.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Um tipo de tupla não pode ser indexado com um valor negativo.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Uma expressão de asserção de tipo não é permitida no lado esquerdo de uma expressão de exponenciação. Considere delimitar a expressão em parênteses.", "A_type_literal_property_cannot_have_an_initializer_1247": "Uma propriedade literal de tipo não pode ter um inicializador.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Uma importação somente de tipo pode especificar uma importação padrão ou associações nomeadas, mas não ambos.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "O predicado de tipo não pode fazer referência a um parâmetro rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "O predicado de tipo não pode fazer referência ao elemento '{0}' em um padrão de associação.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "O predicado de tipo só é permitido na posição de tipo de retorno para funções e métodos.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "O tipo de um predicado de tipo deve ser atribuível para o tipo de seu parâmetro.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Um tipo referenciado em uma assinatura decorada deve ser importado com 'tipo de importação' ou uma importação de namespace quando 'isolatedModules' e 'emitDecoratorMetadata' estão habilitados.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Uma variável cujo tipo é um tipo de 'unique symbol' deve ser 'const'.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "A expressão 'yield' só é permitida em um corpo gerador.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "O método abstrato '{0}' na classe '{1}' não pode ser acessado por meio da expressão super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Os métodos abstratos só podem aparecer dentro de uma classe abstrata.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "As propriedades abstratas só podem aparecer em uma classe abstrata.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "A propriedade abstrata '{0}' na classe '{1}' não pode ser acessada no construtor.", "Accessibility_modifier_already_seen_1028": "O modificador de acessibilidade já foi visto.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Os acessadores somente estão disponíveis no direcionamento para ECMAScript 5 e superior.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Acessadores devem ser abstratos ou não abstratos.", "Add_0_to_unresolved_variable_90008": "Adicionar '{0}.' à variável não resolvida", "Add_a_return_statement_95111": "Adicionar uma instrução return", "Add_a_return_type_to_the_function_declaration_9031": "Adicione um tipo de retorno à declaração da função.", "Add_a_return_type_to_the_function_expression_9030": "Adicionar um tipo de retorno à expressão da função.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "Adicione um tipo de retorno à declaração do acessório Get.", "Add_a_return_type_to_the_method_9034": "Adicione um tipo de retorno ao método", "Add_a_type_annotation_to_the_parameter_0_9028": "Adicione uma anotação de tipo ao parâmetro {0}.", "Add_a_type_annotation_to_the_property_0_9029": "Adicione uma anotação de tipo à propriedade {0}.", "Add_a_type_annotation_to_the_variable_0_9027": "Adicione uma anotação de tipo à variável {0}.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "Adicionar um tipo ao parâmetro da declaração do acessório set.", "Add_all_missing_async_modifiers_95041": "Adicionar todos os modificadores 'async' ausentes", "Add_all_missing_attributes_95168": "Adicionar todos os atributos ausentes", "Add_all_missing_call_parentheses_95068": "Adicionar todos os parênteses de chamada ausentes", "Add_all_missing_function_declarations_95157": "<PERSON><PERSON><PERSON><PERSON> as declarações de função ausentes", "Add_all_missing_imports_95064": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as importações ausentes", "Add_all_missing_members_95022": "Adicionar todos os membros ausentes", "Add_all_missing_override_modifiers_95162": "Adicionar todos os modificadores 'override' ausentes", "Add_all_missing_parameters_95190": "Adicionar todos os parâmetros ausentes", "Add_all_missing_properties_95166": "<PERSON><PERSON><PERSON><PERSON> as propriedades ausentes", "Add_all_missing_return_statement_95114": "<PERSON><PERSON><PERSON><PERSON> as instruções return ausentes", "Add_all_missing_super_calls_95039": "<PERSON><PERSON><PERSON><PERSON> as chamadas super ausentes", "Add_all_missing_type_annotations_90067": "<PERSON><PERSON><PERSON> to<PERSON> as anotações de tipo ausentes", "Add_all_optional_parameters_95193": "Adicione todos os parâmetros opcionais", "Add_annotation_of_type_0_90062": "Adicionar anotação do tipo '{0}'", "Add_async_modifier_to_containing_function_90029": "Adicione o modificador assíncrono que contém a função", "Add_await_95083": "<PERSON><PERSON><PERSON><PERSON> 'await'", "Add_await_to_initializer_for_0_95084": "Adicionar 'await' ao inicializador para '{0}'", "Add_await_to_initializers_95089": "Adicionar 'await' aos inicializadores", "Add_braces_to_arrow_function_95059": "Adicionar chaves para a função de seta", "Add_const_to_all_unresolved_variables_95082": "Adicionar 'const' a todas as variáveis não resolvidas", "Add_const_to_unresolved_variable_95081": "Adicionar 'const' à variável não resolvida", "Add_definite_assignment_assertion_to_property_0_95020": "Adicionar a asserção de atribuição definitiva à propriedade '{0}'", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Adicionar declarações de atribuição definidas a todas as propriedades não inicializadas", "Add_export_to_make_this_file_into_a_module_95097": "Adicionar 'export {}' para transformar este arquivo em um módulo", "Add_extends_constraint_2211": "Adicione a restrição `extends`.", "Add_extends_constraint_to_all_type_parameters_2212": "Adicionar a restrição `extends` a todos os parâmetros de tipo", "Add_import_from_0_90057": "Adicionar importação de \"{0}\"", "Add_index_signature_for_property_0_90017": "Adicionar assinatura de índice para a propriedade '{0}'", "Add_initializer_to_property_0_95019": "Adicionar iniciali<PERSON> à propriedade '{0}'", "Add_initializers_to_all_uninitialized_properties_95027": "Adicionar inicializadores a todas as propriedades não inicializadas", "Add_missing_attributes_95167": "Adicionar atributos ausentes", "Add_missing_call_parentheses_95067": "Adicionar os parênteses de chamada ausentes", "Add_missing_comma_for_object_member_completion_0_95187": "Adicionar vírgula ausente para conclusão de membro de objeto '{0}'.", "Add_missing_enum_member_0_95063": "Adicionar membro de enumeração ausente '{0}'", "Add_missing_function_declaration_0_95156": "Adicionar a declaração de função ausente '{0}'", "Add_missing_new_operator_to_all_calls_95072": "Adicionar operador 'new' ausente a todas as chamadas", "Add_missing_new_operator_to_call_95071": "Adicionar operador 'new' ausente à chamada", "Add_missing_parameter_to_0_95188": "Adicionar parâmetro ausente a '{0}'", "Add_missing_parameters_to_0_95189": "Adicionar parâ<PERSON> ausentes a '{0}'", "Add_missing_properties_95165": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON> ausentes", "Add_missing_super_call_90001": "<PERSON><PERSON><PERSON><PERSON> chamada 'super()' ausente", "Add_missing_typeof_95052": "Adici<PERSON><PERSON> 'typeof' ausente", "Add_names_to_all_parameters_without_names_95073": "Adicionar nomes a todos os parâmetros sem nomes", "Add_optional_parameter_to_0_95191": "Adicionar parâmetro opcional a '{0}'", "Add_optional_parameters_to_0_95192": "Adicionar parâmetros opcionais ao '{0}'", "Add_or_remove_braces_in_an_arrow_function_95058": "<PERSON><PERSON><PERSON><PERSON> ou remover chaves em uma função de seta", "Add_override_modifier_95160": "Adicionar modificador \"override\"", "Add_parameter_name_90034": "Adicionar nome de parâmetro", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Adicionar um qualificador a todas as variáveis não resolvidas correspondentes a um nome de membro", "Add_resolution_mode_import_attribute_95196": "Adicionar o atributo de importação 'resolution-mode'", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "Adicionar o atributo de importação 'resolution-mode' a todas as importações somente de tipo que precisem dele", "Add_return_type_0_90063": "Adicionar tipo de retorno '{0}'", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "Adicione satisfies e uma asserção de tipo a esta expressão (satisfies T as T) para tornar o tipo explícito.", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "Adicionar satisfies e uma asserção de tipo embutido com '{0}'", "Add_to_all_uncalled_decorators_95044": "Adicionar '()' a todos os decoradores não chamados", "Add_ts_ignore_to_all_error_messages_95042": "Adici<PERSON>r '@ts-ignore' a todas as mensagens de erro", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Adicione 'indefinido' a um tipo quando acessado usando um índice.", "Add_undefined_to_optional_property_type_95169": "Adicionar 'undefined' ao tipo de propriedade opcional", "Add_undefined_type_to_all_uninitialized_properties_95029": "Adicionar tipo indefinido a todas as propriedades não inicializadas", "Add_undefined_type_to_property_0_95018": "Adicionar tipo 'indefinido' à propriedade '{0}'", "Add_unknown_conversion_for_non_overlapping_types_95069": "Adicionar conversão 'unknown' para tipos sem sobreposição", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Adicionar 'unknown' a todas as conversões de tipos sem sobreposição", "Add_void_to_Promise_resolved_without_a_value_95143": "Adicionar 'void' ao Promise resolvido sem um valor", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Adicionar 'void' a todos os Promises resolvidos sem um valor", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Adicionar um arquivo tsconfig.json ajudará a organizar projetos que contêm arquivos TypeScript e JavaScript. Saiba mais em https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "<PERSON><PERSON> as declarações de '{0}' devem ter restrições idênticas.", "All_declarations_of_0_must_have_identical_modifiers_2687": "<PERSON><PERSON> as declar<PERSON><PERSON><PERSON><PERSON> de '{0}' devem ter modificadores idênticos.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "<PERSON><PERSON> as declar<PERSON><PERSON><PERSON><PERSON> de '{0}' devem ter parâmetros de tipo idênticos.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "<PERSON><PERSON> as declarações de um método abstrato devem ser consecutivas.", "All_destructured_elements_are_unused_6198": "Todos os elementos desestruturados são inutilizados.", "All_imports_in_import_declaration_are_unused_6192": "Nenhuma das importações na declaração de importação está sendo utilizada.", "All_type_parameters_are_unused_6205": "Todos os parâmetros de tipo são inutilizados.", "All_variables_are_unused_6199": "Nenhuma das variáveis está sendo utilizada.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Permitir que arquivos JavaScript façam parte do seu programa. Use a opção 'checkJS' para obter erros desses arquivos.", "Allow_accessing_UMD_globals_from_modules_6602": "Permitir o acesso a UMD globais de módulos.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Permita importações padrão de módulos sem exportação padrão. Isso não afeta a emissão do código, apenas a verificação de digitação.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Permitir 'importar x de y' quando um módulo não tiver uma exportação padrão.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Permitir a importação de funções auxiliares do tslib uma vez por projeto, em vez de incluí-las por arquivo.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "Permitir que as importações incluam extensões de arquivo TypeScript. Requer a definição de '--moduleResolution bundler' e '--noEmit' ou '--emitDeclarationOnly'.", "Allow_javascript_files_to_be_compiled_6102": "Permita que arquivos javascript sejam compilados.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Per<PERSON><PERSON> que várias pastas sejam tratadas como uma ao resolver módulos.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "O nome do arquivo '{0}' já incluído difere do nome de arquivo '{1}' somente em maiúsculas e minúsculas.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "A declaração de módulo de ambiente não pode especificar o nome do módulo relativo.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Módulos de ambiente não podem ser aninhados em outros módulos ou namespaces.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Um módulo AMD não pode ter várias atribuições de nome.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Um acessador abstrato não pode ter uma implementação.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Não é possível usar um modificador de acessibilidade com um identificador privado.", "An_accessor_cannot_have_type_parameters_1094": "Um acessador não pode ter parâmetros de tipo.", "An_accessor_property_cannot_be_declared_optional_1276": "<PERSON>a propriedade 'acessador' não pode ser declarada opcional.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Uma declaração de módulo de ambiente só é permitida no nível superior em um arquivo.", "An_argument_for_0_was_not_provided_6210": "Não foi fornecido um argumento para '{0}'.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Não foi fornecido um argumento correspondente a esse padrão de associação.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Um operando aritmético deve ser do tipo 'any', 'number', 'bignit' ou um tipo enumerado.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Uma função de seta não pode ter um parâmetro 'this'.", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "Uma função ou método assíncrono no ES5 requer o construtor 'Promise'.  Verifique se você tem uma declaração para o construtor 'Promise' ou inclua 'ES2015' na sua opção '--lib'.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Uma função ou método assíncrono deve retornar um 'Promise'. Verifique se você tem uma declaração para 'Promise' ou inclua 'ES2015' na sua opção '--lib'.", "An_async_iterator_must_have_a_next_method_2519": "O iterador assíncrono deve ter um método 'next()'.", "An_element_access_expression_should_take_an_argument_1011": "Uma expressão de acesso do elemento deveria receber um argumento.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Um membro de enumeração não pode ser nomeado com um identificador privado.", "An_enum_member_cannot_have_a_numeric_name_2452": "Um membro de enumeração não pode ter um nome numérico.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Um nome de membro de enumeração deve ser seguido por ',', '=' ou '}'.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Uma versão expandida dessas informações, mostrando todas as opções do compilador possíveis", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Uma atribuição de exportação não pode ser usada em um módulo com outros elementos exportados.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Uma atribuição de exportação não pode ser usada em um namespace.", "An_export_assignment_cannot_have_modifiers_1120": "Uma atribuição de exportação não pode ter modificadores.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Uma atribuição de exportação deve estar no nível superior de uma declaração de arquivo ou módulo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Uma declaração de exportação só pode ser usada no nível superior de um módulo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Uma declaração de exportação só pode ser usada no nível superior de um namespace ou módulo.", "An_export_declaration_cannot_have_modifiers_1193": "Uma declaração de exportação não pode ter modificadores.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "Uma declaração 'export =' deve fazer referência a um valor real quando 'verbatimModuleSyntax' estiver habilitado, mas '{0}' resolve para uma declaração somente de tipo.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "Uma declaração 'export =' deve fazer referência a um valor quando 'verbatimModuleSyntax' estiver habilitado, mas '{0}' refere-se apenas a um tipo.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "Um 'export default' deve referenciar um valor real quando 'verbatimModuleSyntax' estiver habilitado, mas '{0}' resolve para uma declaração somente de tipo.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "Um 'export default' deve referenciar um valor real quando 'verbatimModuleSyntax' estiver habilitado, mas '{0}' se refere somente a um tipo.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "Uma expressão do tipo 'nula' não pode ser testada quanto à veracidade.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Um valor de escape Unicode estendido deve estar entre 0x0 e 0x10FFFF, inclusive.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Um identificador ou palavra-chave não pode imediatamente seguir um literal numérico.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Uma implementação não pode ser declarada em contextos de ambiente.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Um alias de importação não pode fazer referência a uma declaração que foi exportada usando 'tipo de exportação'.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Um alias de importação não pode fazer referência a uma declaração que foi importada usando 'tipo de importação'.", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "Um alias de importação não pode resolver uma declaração de tipo ou somente tipo quando \"verbatimModuleSyntax\" estiver habilitado.", "An_import_alias_cannot_use_import_type_1392": "Um alias de importação não pode usar um 'tipo de importação'", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Uma declaração de importação só pode ser usada no nível superior de um módulo.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Uma declaração de importação só pode ser usada no nível superior de um namespace ou módulo.", "An_import_declaration_cannot_have_modifiers_1191": "Uma declaração de importação não pode ter modificadores.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "Um caminho de importação só pode terminar com uma extensão '{0}' quando 'allowImportingTsExtensions' estiver habilitado.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Uma assinatura de índice não pode ter um parâmetro rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Uma assinatura de índice não pode ter uma vírgula à direita.", "An_index_signature_must_have_a_type_annotation_1021": "Uma assinatura de índice deve ter uma anotação de tipo.", "An_index_signature_must_have_exactly_one_parameter_1096": "Uma assinatura de índice deve ter exatamente um parâmetro.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Um parâmetro de assinatura de índice não pode ter um ponto de interrogação.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Um parâmetro de assinatura de índice não pode ter um modificador de acessibilidade.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Um parâmetro de assinatura de índice não pode ter um inicializador.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Um parâmetro de assinatura de índice deve ter uma anotação de tipo.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Um tipo de parâmetro de assinatura de índice não pode ser um tipo literal ou genérico. Considere usar um tipo de objeto mapeado.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Um tipo de parâmetro de assinatura de índice deve ser 'cadeia de caracteres', 'número', 'sí<PERSON>lo' ou um tipo literal de modelo.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Uma expressão de instanciação não pode ser seguida por um acesso de propriedade.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Uma interface só pode estender um identificador/nome qualificado com argumentos de tipo opcionais.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Uma interface só pode estender um tipo de objeto ou interseção de tipos de objeto com membros estaticamente conhecidos.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "Uma interface não pode estender um tipo primitivo como '{0}'. Ela só pode estender outros tipos de objetos nomeados.", "An_interface_property_cannot_have_an_initializer_1246": "Uma propriedade de interface não pode ter um inicializador.", "An_iterator_must_have_a_next_method_2489": "Um iterador deve ter um método 'next()'.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "Um pragma @jsxFrag é necessário ao usar um pragma @jsx com fragmentos JSX.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Um literal de objeto não pode ter vários acessadores get/set com o mesmo nome.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Um literal de objeto não pode ter várias propriedades com o mesmo nome.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Um literal de objeto não pode ter propriedade e acessador com o mesmo nome.", "An_object_member_cannot_be_declared_optional_1162": "Um membro de objeto não pode ser declarado como opcional.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "O método '[Symbol.hasInstance]' de um objeto deve retornar um valor booliano para que ele seja usado no lado direito de uma expressão 'instanceof'.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Uma cadeia opcional não pode conter identificadores privados.", "An_optional_element_cannot_follow_a_rest_element_1266": "Um elemento opcional não pode seguir um elemento REST.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Um valor externo de 'this' é sombreado por este contêiner.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "A assinatura de sobrecarga não pode ser declarada como geradora.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Uma expressão unária com o operador '{0}' não é permitida no lado esquerdo de uma expressão de exponenciação. Considere delimitar a expressão em parênteses.", "Annotate_everything_with_types_from_JSDoc_95043": "Anotar tudo com tipos do JSDoc", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "Anotar tipos de propriedades da função expando em um namespace", "Annotate_with_type_from_JSDoc_95009": "Anotar com o tipo do JSDoc", "Another_export_default_is_here_2753": "Outro padrão de exportação está aqui.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "Qualquer propriedade Unicode que possa corresponder a mais de um único caractere só estará disponível quando o sinalizador Conjuntos Unicode (v) estiver definido.", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "Qualquer coisa que possa corresponder a mais de um único caractere é inválida dentro de uma classe de caractere negada.", "Are_you_missing_a_semicolon_2734": "Você está esquecendo de um ponto e vírgula?", "Argument_expression_expected_1135": "Expressão de argumento esperada.", "Argument_for_0_option_must_be_Colon_1_6046": "O argumento para a opção '{0}' deve ser: {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "O argumento da importação dinâmica não pode ser um elemento de espalhamento.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "O argumento do tipo '{0}' não é atribuível ao parâmetro do tipo '{1}'.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "O argumento do tipo '{0}' não pode ser atribuído ao parâmetro do tipo '{1}' com 'exactOptionalPropertyTypes: true'. Considere adicionar 'undefined' aos tipos das propriedades do destino.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "<PERSON>ão foram fornecidos argumentos para o parâmetro REST '{0}'.", "Array_element_destructuring_pattern_expected_1181": "Padrão de desestruturação de elemento da matriz esperado.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "Matrizes com elementos espalhados não podem ser inferidas com --isolatedDeclarations.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "As declarações exigem que todos os nomes no destino de chamada sejam declarados com uma anotação de tipo explícito.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "As declarações exigem que o destino da chamada seja um identificador ou um nome qualificado.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "Não há suporte para a atribuição de propriedades a funções sem declará-las com --isolatedDeclarations. Adicione uma declaração explícita para as propriedades atribuídas a essa função.", "Asterisk_Slash_expected_1010": "'*/' esperado.", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "Pelo menos um acessor deve ter uma anotação de tipo explícita com `--isolatedDeclarations.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Acréscimos de escopo global somente podem ser diretamente aninhados em módulos externos ou declarações de módulo de ambiente.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Acréscimos de escopo global devem ter o modificador 'declare' a menos que apareçam em contexto já ambiente.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "A descoberta automática para digitações está habilitada no projeto '{0}'. Executando o passe de resolução extra para o módulo '{1}' usando o local do cache '{2}'.", "BUILD_OPTIONS_6919": "OPÇÕES DE BUILD", "Backwards_Compatibility_6253": "Compatibilidade com Versões Anteriores", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "As expressões de classe base não podem referenciar parâmetros de tipo de classe.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "O tipo de retorno do construtor base '{0}' não é um tipo de objeto ou interseção de tipos de objeto com membros estaticamente conhecidos.", "Base_constructors_must_all_have_the_same_return_type_2510": "Todos os construtores base devem ter o mesmo tipo de retorno.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Diretório base para resolver nomes de módulo não absolutos.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "Os literais de BigInt não estão disponíveis ao direcionar para menos de ES2020.", "Binary_digit_expected_1177": "<PERSON><PERSON><PERSON><PERSON> esperado.", "Binding_element_0_implicitly_has_an_1_type_7031": "O elemento de associação '{0}' tem implicitamente um tipo '{1}'.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "Os elementos de associação não podem ser exportados diretamente com --isolatedDeclarations.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Variável de escopo de bloco '{0}' usada antes da sua declaração.", "Build_a_composite_project_in_the_working_directory_6925": "Crie um projeto composto no diretório de trabalho.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Compilar todos os projetos, incluindo aqueles que parecem estar atualizados.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Compilar um ou mais projetos e suas dependências, se estiverem desatualizados", "Build_option_0_requires_a_value_of_type_1_5073": "A opção de build '{0}' requer um valor do tipo {1}.", "Building_project_0_6358": "Compilando o projeto '{0}'...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "Iteradores internos são instanciados com um tipo 'TReturn' de 'undefined' em vez de 'any'.", "COMMAND_LINE_FLAGS_6921": "SINALIZADORES DE LINHA DE COMANDO", "COMMON_COMMANDS_6916": "COMANDOS COMUNS", "COMMON_COMPILER_OPTIONS_6920": "OPÇÕES COMUNS DO COMPILADOR", "Call_decorator_expression_90028": "Chamar expressão do decorador", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Os tipos de retorno da assinatura de chamada '{0}' e '{1}' são incompatíveis.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Assinatura de chamada, que não tem a anotação de tipo de retorno, implicitamente tem um tipo de retorno 'any'.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Assinaturas de chamada sem argumentos têm tipos de retorno incompatíveis '{0}' e '{1}'.", "Can_only_convert_logical_AND_access_chains_95142": "Só é possível converter cadeias de acesso E lógicas", "Can_only_convert_named_export_95164": "Só pode converter exportação nomeada", "Can_only_convert_property_with_modifier_95137": "<PERSON><PERSON> <PERSON> possível converter a propriedade com o modificador", "Can_only_convert_string_concatenations_and_string_literals_95154": "Só é possível converter concatenações de cadeia de caracteres e literais de cadeia de caracteres", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Não foi possível acessar '{0}.{1}' porque '{0}' é um tipo, mas não um namespace. Você quis dizer recuperar o tipo da propriedade '{1}' em '{0}' com '{0}[\"{1}\"]'?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "Não é possível acessar '{0}' de outro arquivo sem qualificação quando '{1}' estiver habilitado. Use '{2}' em vez disso.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "Não é possível acessar enumerações const de ambiente quando '{0}' estiver habilitado.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Não é possível atribuir um tipo de construtor '{0}' para um tipo de construtor '{1}'.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Não é possível atribuir um tipo de construtor abstrato a um tipo de construtor não abstrato.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Não é possível fazer a atribuição a '{0}' porque ela é uma classe.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Não é possível atribuir a '{0}' porque é uma constante.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Não é possível fazer a atribuição a '{0}' porque ela é uma função.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Não é possível fazer a atribuição a '{0}' porque ele é um namespace.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Não é possível atribuir a '{0}' porque é uma propriedade de somente leitura.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Não é possível fazer a atribuição a '{0}' porque ela é uma enumeração.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Não é possível fazer a atribuição a '{0}' porque ela é uma importação.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Não é possível atribuir a '{0}' porque não é uma variável.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Não é possível fazer a atribuição ao método privado '{0}'. Os métodos privados não são graváveis.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Não é possível aumentar o módulo '{0}' porque ele resolve para uma entidade não módulo.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Não é possível aumentar o módulo '{0}' com as exportações do valor, porque ele resolve para uma entidade sem módulo.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Não é possível compilar módulos usando a opção '{0}', a menos que o sinalizador '--module' seja 'amd' ou 'system'.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Não é possível criar uma instância de uma classe abstrata.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Não é possível delegar iteração para valor porque o método 'next' de seu iterador espera o tipo '{1}', mas o gerador que a contém sempre enviará '{0}'.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Não é possível exportar '{0}'. Somente declarações locais podem ser exportadas de um módulo.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Não é possível estender uma classe '{0}'. O construtor de classe está marcado como privado.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Não é possível estender uma interface '{0}'. Você quis dizer 'implements'?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Não é possível localizar um arquivo tsconfig.json no diretório atual: {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Não é possível encontrar um arquivo tsconfig.json no diretório especificado: '{0}'.", "Cannot_find_global_type_0_2318": "Não é possível encontrar o tipo global '{0}'.", "Cannot_find_global_value_0_2468": "Não é possível encontrar o valor global '{0}'.", "Cannot_find_lib_definition_for_0_2726": "Não é possível encontrar a definição de biblioteca para '{0}'.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Não é possível encontrar a definição de biblioteca para '{0}'. Você quis dizer '{1}'?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Não é possível localizar o módulo '{0}'. Considere usar '--resolveJsonModule' para importar o módulo com a extensão '.json'.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "Não é possível localizar o módulo '{0}'. Você quis definir a opção 'moduleResolution' como 'nodenext' ou adicionar aliases à opção 'paths'?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "Não é possível localizar o módulo '{0}' ou suas declarações de tipo correspondentes.", "Cannot_find_name_0_2304": "Não é possível encontrar o nome '{0}'.", "Cannot_find_name_0_Did_you_mean_1_2552": "Não é possível localizar o nome '{0}'. Você quis dizer '{1}'?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Não foi possível localizar o nome '{0}'. Voc<PERSON> quis dizer o membro de instância 'this.{0}'?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Não foi possível encontrar o nome '{0}'. Você quis dizer o membro estático '{1}.{0}'?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "Não foi possível encontrar o nome '{0}'. Voc<PERSON> quis escrever isto em uma função assíncrona?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Não é possível encontrar o nome '{0}'. Você precisa alterar sua biblioteca de destino? Tente alterar a opção 'lib' do compilador para '{1}' ou posterior.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Não é possível encontrar o nome '{0}'. Você precisa alterar sua biblioteca de destino? Tente alterar a opção 'lib' do compilador para incluir 'dom'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "Não é possível encontrar o nome '{0}'. Você precisa instalar definições de tipo para o Bun? Tente 'npm i --save-dev @types/bun'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "Não é possível encontrar o nome '{0}'. Você precisa instalar definições de tipo para o Bun? Tente 'npm i --save-dev @types/bun' e adicione 'bun' ao campo de tipos em seu tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Não é possível localizar o nome '{0}'. Você precisa instalar definições de tipo para um executor de teste? Tente `npm i --save-dev @types/jest` ou `npm i --save-dev @types/mocha`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Não é possível encontrar o nome '{0}'. Você precisa instalar as definições de tipo para um executor de teste? Tente `npm i --save-dev @types/jest` ou `npm i --save-dev @types/mocha` e depois adicione 'jest' ou 'mocha' ao campo tipos em seu tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Não é possível localizar o nome '{0}'. Você precisa instalar definições de tipo para o jQuery? Tente `npm i --save-dev @types/jquery`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Não é possível localizar o nome '{0}'. Você precisa instalar as definições de tipo para jQuery? Tente `npm i --save-dev @types/jquery` e, em seguida, adicione 'jquery' para o campo tipos em seu tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Não é possível localizar o nome '{0}'. Você precisa instalar definições de tipo para o nó? Tente `npm i --save-dev @types/node`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Não é possível encontrar o nome '{0}'. Você precisa instalar as definições de tipo para o nó? Tente `npm i --save-dev @types/node` e, em seguida, adicione 'node' ao campo tipos em seu tsconfig.", "Cannot_find_namespace_0_2503": "Não é possível encontrar o namespace '{0}'.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Não é possível localizar o namespace '{0}'. Você quis dizer '{1}'?", "Cannot_find_parameter_0_1225": "Não é possível encontrar o parâmetro '{0}'.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Não é possível encontrar o caminho do subdiretório comum para os arquivos de entrada.", "Cannot_find_type_definition_file_for_0_2688": "Não é possível encontrar o arquivo de definição de tipo para '{0}'.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Não é possível importar arquivos de declaração de tipo. Considere a possibilidade de importar '{0}' em vez de '{1}'.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Não é possível inicializar a variável com escopo externo '{0}' no mesmo escopo que a declaração de escopo de bloco '{1}'.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Não é possível invocar um objeto que é possivelmente 'nulo'.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Não é possível invocar um objeto que é possivelmente 'nulo' ou 'indefinido'.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Não é possível invocar um objeto que é possivelmente 'indefinido'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Não é possível iterar o valor porque o método 'next' de seu iterador espera o tipo '{1}', mas a desestruturação da matriz sempre enviará '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Não é possível iterar o valor porque o método 'next' de seu iterador espera o tipo '{1}', mas o array spread sempre enviará '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Não é possível iterar o valor porque o método 'next' de seu iterador espera o tipo '{1}', mas a instrução for-of sempre enviará '{0}'.", "Cannot_move_statements_to_the_selected_file_95183": "Não é possível mover instruções para o arquivo selecionado", "Cannot_move_to_file_selected_file_is_invalid_95179": "Não é possível mover para o arquivo, o arquivo selecionado é inválido", "Cannot_read_file_0_5083": "Não é possível ler o arquivo '{0}'.", "Cannot_read_file_0_Colon_1_5012": "Não é possível ler o arquivo '{0}': {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Não é possível declarar novamente a variável de escopo de bloco '{0}'.", "Cannot_redeclare_exported_variable_0_2323": "Não é possível redeclarar a variável exportada '{0}'.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Não é possível declarar novamente o identificador '{0}' na cláusula catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Não é possível iniciar uma chamada de função em uma anotação de tipo.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Não é possível usar JSX, a menos que o sinalizador '--jsx' seja fornecido.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "Não é possível usar 'export import' em um tipo ou namespace somente de tipo quando '{0}' estiver habilitado.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Não será possível usar importações, exportações ou acréscimos de módulo quando '--module' for 'none'.", "Cannot_use_namespace_0_as_a_type_2709": "Não é possível usar o namespace '{0}' como um tipo.", "Cannot_use_namespace_0_as_a_value_2708": "Não é possível usar o namespace '{0}' como um valor.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Não é possível usar \"this\" em um inicializador de propriedade estática de uma classe decorada.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Não é possível gravar o arquivo '{0}' porque ele substituirá o arquivo '.tsbuildinfo' gerado pelo projeto referenciado '{1}'", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Não é possível gravar o arquivo '{0}' porque ele seria substituído por diversos arquivos de entrada.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Não é possível gravar o arquivo '{0}' porque ele substituiria o arquivo de entrada.", "Catch_clause_variable_cannot_have_an_initializer_1197": "A variável de cláusula catch não pode ter um inicializador.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "A anotação de tipo de variável da cláusula catch precisa ser 'any' ou 'unknown' quando especificada.", "Change_0_to_1_90014": "Alterar '{0}' para '{1}'", "Change_all_extended_interfaces_to_implements_95038": "Alterar todas as interfaces estendidas para 'implements'", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Alterar todos os tipos de estilo jsdoc para TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Alterar todos os tipos de estilo jsdoc para TypeScript (e adicionar '| undefined' a tipos que permitem valor nulo)", "Change_extends_to_implements_90003": "Alterar 'extends' para 'implements'", "Change_spelling_to_0_90022": "Alterar ortografia para '{0}'", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "<PERSON><PERSON><PERSON><PERSON> as propriedades de classe declaradas, mas não definidas no construtor.", "Check_side_effect_imports_6806": "Verifique as importações de efeitos colaterais.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Verificar se os argumentos para os métodos 'associar', 'chamar' e 'aplicar' correspondem à função original.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "Verificando se '{0}' é o maior prefixo correspondente para '{1}' - '{2}'.", "Circular_definition_of_import_alias_0_2303": "Definição circular do alias de importação '{0}'.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Circularidade detectada ao resolver a configuração: {0}", "Circularity_originates_in_type_at_this_location_2751": "A circularidade é originada no tipo neste local.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "A classe '{0}' define o acessador de membro de instância '{1}', mas a classe estendida '{2}' o define como uma função de membro de instância.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "A classe '{0}' define a função de membro de instância '{1}', mas a classe estendida '{2}' a define como um acessador de membro de instância.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "A classe '{0}' define a propriedade de membro de instância '{1}', mas a classe estendida '{2}' a define como uma função de membro de instância.", "Class_0_incorrectly_extends_base_class_1_2415": "A classe '{0}' estende incorretamente a classe base '{1}'.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "A classe '{0}' implementa incorretamente a classe '{1}'. Você pretendia estender '{1}' e herdar seus membros como uma subclasse?", "Class_0_incorrectly_implements_interface_1_2420": "A classe '{0}' implementa incorretamente a interface '{1}'.", "Class_0_used_before_its_declaration_2449": "Classe '{0}' usada antes de sua declaração.", "Class_constructor_may_not_be_a_generator_1368": "O construtor de classe não pode ser um gerador.", "Class_constructor_may_not_be_an_accessor_1341": "O construtor de classe não pode ser um acessador.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "A declaração da classe não pode implementar a lista de sobrecarga para '{0}'.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "As declarações de classe não podem ter mais de uma marca '@augments' ou '@extends'.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Os decoradores de classe não podem ser usados com um identificador privado estático. Considere remover o decorador experimental.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "O campo de classe '{0}' definido pela classe pai não está acessível na classe filho via super.", "Class_name_cannot_be_0_2414": "O nome de classe não pode ser '{0}'.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "O nome da classe não pode ser 'Object' ao direcionar ES5 com módulo {0}.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "O lado estático da classe '{0}' incorretamente estende o lado estático da classe base '{1}'.", "Classes_can_only_extend_a_single_class_1174": "Classes só podem estender uma única classe.", "Classes_may_not_have_a_field_named_constructor_18006": "Classes não podem ter um campo denominado 'constructor'.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "O código contido em uma classe é avaliado no modo estrito do JavaScript que não permite o uso de '{0}'. Para obter mais informações, consulte https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Opções da Linha de Comando", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Compile o projeto dando o caminho para seu arquivo de configuração ou para uma pasta com um 'tsconfig.json'.", "Compiler_Diagnostics_6251": "Diagnóstico do Compilador", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "A opção do compilador '{0}' não pode receber uma cadeia de caracteres vazia.", "Compiler_option_0_expects_an_argument_6044": "A opção do compilador '{0}' espera um argumento.", "Compiler_option_0_may_not_be_used_with_build_5094": "A opção de compilador '--{0}' não pode ser usada com '--build'.", "Compiler_option_0_may_only_be_used_with_build_5093": "A opção de compilador '--{0}' pode ser usada somente com '--build'.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "A opção do compilador '{0}' de valor '{1}' é instável. Use o TypeScript noturno para silenciar esse erro. Tente atualizar com 'npm install -D typescript@next'.", "Compiler_option_0_requires_a_value_of_type_1_5024": "A opção do compilador '{0}' requer um valor do tipo {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "O compilador reserva o nome '{0}' ao emitir um identificador privado para versões anteriores.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Compila o projeto TypeScript localizado no caminho especificado.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Compila o projeto atual (tsconfig.json no diretório de trabalho).", "Compiles_the_current_project_with_additional_settings_6929": "Compila o projeto atual, com configurações adicionais.", "Completeness_6257": "Integridade", "Composite_projects_may_not_disable_declaration_emit_6304": "Projetos compostos não podem desabilitar a emissão de declaração.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Projetos compostos podem não desabilitar a compilação incremental.", "Computed_from_the_list_of_input_files_6911": "Calculado a partir da lista de arquivos de entrada", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "As propriedades computadas devem ser literais de números ou cadeia de caracteres, variáveis ou expressões pontilhadas com --isolatedDeclarations.", "Computed_property_names_are_not_allowed_in_enums_1164": "Nomes de propriedade calculados não são permitidos em enums.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "Nomes de propriedades computadas em literais de classe ou objeto não podem ser inferidos com --isolatedDeclarations.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Os valores computados não são permitidos em uma enumeração com membros de valor de cadeia de caracteres.", "Concatenate_and_emit_output_to_single_file_6001": "Concatenar e emitir saída para um arquivo único.", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "Condições a serem definidas além dos padrões específicos do resolvedor ao resolver importações.", "Conflicts_are_in_this_file_6201": "Há conflitos neste arquivo.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Considere adicionar um modificador 'declare' para esta classe.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Os tipos de retorno de assinatura de constructo '{0}' e '{1}' são incompatíveis.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Assinatura de constructo, que não tem a anotação de tipo de retorno, implicitamente tem um tipo de retorno 'any'.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Assinaturas de constructo sem argumentos têm tipos de retorno incompatíveis '{0}' e '{1}'.", "Constructor_implementation_is_missing_2390": "Implementação do construtor ausente.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "O construtor de classe '{0}' é privado e somente acessível na declaração de classe.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "O construtor de classe '{0}' é protegido e somente acessível na declaração de classe.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "A notação de tipo de construtor precisa estar entre parênteses quando usada em um tipo de união.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "A notação de tipo de construtor precisa estar entre parênteses quando usada em um tipo de interseção.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Construtores para classes derivadas devem conter uma chamada 'super'.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "O arquivo contido não foi especificado e o diretório raiz não pode ser determinado, ignorando a pesquisa na pasta 'node_modules'.", "Containing_function_is_not_an_arrow_function_95128": "A função contentora não é uma função de seta", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Controlar qual método é usado para detectar arquivos JS no formato de módulo.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "A conversão do tipo '{0}' para o tipo '{1}' pode ser um erro porque nenhum tipo está suficientemente sobreposto ao outro. Se isso era intencional, converta a expressão para 'unknown' primeiro.", "Convert_0_to_1_in_0_95003": "Converter '{0}' em '{1} em {0}'", "Convert_0_to_mapped_object_type_95055": "Converter '{0}' para o tipo de objeto mapeado", "Convert_all_const_to_let_95102": "Converta todos os 'const' para 'let'", "Convert_all_constructor_functions_to_classes_95045": "Converter todas as funções de construtor em classes", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Converter todos os caracteres inválidos em código de entidade HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Converter todos os tipos re-exportados para exportações somente de tipo", "Convert_all_require_to_import_95048": "Converter todos os 'require' em 'import'", "Convert_all_to_async_functions_95066": "Converter todos para funções assíncronas", "Convert_all_to_bigint_numeric_literals_95092": "Converter todos para literais numéricos bigint", "Convert_all_to_default_imports_95035": "Converter tudo para importações padrão", "Convert_all_type_literals_to_mapped_type_95021": "Converter todos os literais de tipo em tipo mapeado", "Convert_all_typedef_to_TypeScript_types_95177": "Converta todos os typedef em tipos TypeScript.", "Convert_arrow_function_or_function_expression_95122": "Converter a função de seta ou a expressão de função", "Convert_const_to_let_95093": "Converter 'const' para 'let'", "Convert_default_export_to_named_export_95061": "Converter exportação padrão para exportação nomeada", "Convert_function_declaration_0_to_arrow_function_95106": "Converter a declaração de função '{0}' em função de seta", "Convert_function_expression_0_to_arrow_function_95105": "Converter a expressão de função '{0}' em função de seta", "Convert_function_to_an_ES2015_class_95001": "Converter função em uma classe ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Converter o caractere inválido para seu código de entidade HTML", "Convert_named_export_to_default_export_95062": "Converter a exportação nomeada para a exportação padrão", "Convert_named_imports_to_default_import_95170": "Converter importações nomeadas em importação padrão", "Convert_named_imports_to_namespace_import_95057": "Converter importações nomeadas em importação de namespace", "Convert_namespace_import_to_named_imports_95056": "Converter importação de namespace em importações nomeadas", "Convert_overload_list_to_single_signature_95118": "Converter a lista de sobrecarga em assinatura única", "Convert_parameters_to_destructured_object_95075": "Converter parâmetros para objeto não estruturado", "Convert_require_to_import_95047": "Converter 'require' em 'import'", "Convert_to_ES_module_95017": "Converter em módulo <PERSON>", "Convert_to_a_bigint_numeric_literal_95091": "Converter para um literal numérico bigint", "Convert_to_anonymous_function_95123": "Converter em uma função anônima", "Convert_to_arrow_function_95125": "Converter em uma função de seta", "Convert_to_async_function_95065": "Converter para uma função assíncrona", "Convert_to_default_import_95013": "Converter para importação padrão", "Convert_to_named_function_95124": "Converter em uma função nomeada", "Convert_to_optional_chain_expression_95139": "Converter em expressão de cadeia opcional", "Convert_to_template_string_95096": "Converter para cadeia de caracteres de modelo", "Convert_to_type_only_export_1364": "Converter para exportação somente de tipo", "Convert_typedef_to_TypeScript_type_95176": "Converta o typedef em um tipo TypeScript.", "Corrupted_locale_file_0_6051": "Arquivo de localidade {0} corrompido.", "Could_not_convert_to_anonymous_function_95153": "Não foi possível fazer a conversão para a função anônima", "Could_not_convert_to_arrow_function_95151": "Não foi possível fazer a conversão para a função de seta", "Could_not_convert_to_named_function_95152": "Não foi possível fazer a conversão para a função nomeada", "Could_not_determine_function_return_type_95150": "Não foi possível determinar o tipo de retorno da função", "Could_not_find_a_containing_arrow_function_95127": "Não foi possível localizar uma função de seta contentora", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Não foi possível localizar o arquivo de declaração para o módulo '{0}'. '{1}' tem implicitamente um tipo 'any'.", "Could_not_find_convertible_access_expression_95140": "Não foi possível localizar a expressão de acesso conversível", "Could_not_find_export_statement_95129": "Não foi possível localizar a instrução de exportação", "Could_not_find_import_clause_95131": "Não foi possível localizar a cláusula de importação", "Could_not_find_matching_access_expressions_95141": "Não foi possível localizar expressões de acesso correspondentes", "Could_not_find_name_0_Did_you_mean_1_2570": "Não foi possível encontrar o nome '{0}'. Voc<PERSON> quis dizer '{1}'?", "Could_not_find_namespace_import_or_named_imports_95132": "Não foi possível localizar a importação de namespace nem as importações nomeadas", "Could_not_find_property_for_which_to_generate_accessor_95135": "Não foi possível localizar a propriedade para a qual o acessador deve ser gerado", "Could_not_find_variable_to_inline_95185": "Não foi possível localizar a variável para embutida.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Não foi possível resolver o caminho '{0}' com as extensões: {1}.", "Could_not_write_file_0_Colon_1_5033": "Não foi possível gravar o arquivo '{0}': {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Criar arquivos source map para arquivos JavaScript emitidos.", "Create_sourcemaps_for_d_ts_files_6614": "Criar sourcemaps para arquivos .d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Cria um tsconfig.json com as configurações recomendadas no diretório de trabalho.", "DIRECTORY_6038": "DIRETÓRIO", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "Sequências de escape decimais e referências inversas não são permitidas em uma classe de caracteres.", "Decimals_with_leading_zeros_are_not_allowed_1489": "Decimais com zeros à esquerda não são permitidos.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "A declaração aumenta a declaração em outro arquivo. Isso não pode ser serializado.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "A emissão de declaração para este arquivo requer a preservação desta importação para aumentos. Não há suporte para isso com --isolatedDeclarations.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "A emissão de declaração para esse arquivo requer o uso do nome privado '{0}'. Uma anotação de tipo explícita pode desbloquear a emissão de declaração.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "A emissão de declaração para esse arquivo requer o uso do nome privado '{0}' do módulo '{1}'. Uma anotação de tipo explícita pode desbloquear a emissão de declaração.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "A declaração emit para esse parâmetro requer a adição implícita de indefinido ao seu tipo. Não há suporte para isso com --isolatedDeclarations.", "Declaration_expected_1146": "Declaração esperada.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "O nome de declaração entra em conflito com o identificador global integrado '{0}'.", "Declaration_or_statement_expected_1128": "Declaração ou instrução esperada.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Declaração ou instrução esperada. Esse \"=\" segue um bloco de instruções, portanto, se você pretendia escrever uma atribuição de desestruturação, talvez seja necessário encapsular toda a atribuição entre parênteses.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "As declarações com asserções de atribuição definitiva também precisam ter anotações de tipo.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "As declarações com inicializadores também não podem ter asserções de atribuição definitiva.", "Declare_a_private_field_named_0_90053": "Declare um campo privado chamado '{0}'.", "Declare_method_0_90023": "Declarar mé<PERSON>do '{0}'", "Declare_private_method_0_90038": "Declarar método privado '{0}'", "Declare_private_property_0_90035": "Declarar a propriedade privada '{0}'", "Declare_property_0_90016": "<PERSON>larar propriedade '{0}'", "Declare_static_method_0_90024": "Declarar mé<PERSON>do est<PERSON> '{0}'", "Declare_static_property_0_90027": "<PERSON><PERSON><PERSON> propriedade estática '{0}'", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "O tipo de retorno da função decorador '{0}' não é atribuível ao tipo '{1}'.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "O tipo de retorno da função decorador é '{0}' mas deve ser 'void' ou 'any'.", "Decorator_used_before_export_here_1486": "Decorador usado antes de 'export' aqui.", "Decorators_are_not_valid_here_1206": "Os decoradores não são válidos aqui.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Os decoradores não podem ser aplicados a vários acessadores get/set de mesmo nome.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "Os decoradores podem não aparecer após 'export' ou 'export default' se também aparecerem antes de 'export'.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Os decoradores devem preceder o nome e todas as palavras-chave das declarações de propriedade.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "<PERSON>dronize as vari<PERSON><PERSON><PERSON> da cláusula catch como 'desconhecido' em vez de 'qualquer'.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "A exportação padrão do módulo tem ou está usando o nome particular '{0}'.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "As exportações padrão não podem ser inferidas com --isolatedDeclarations.", "Default_library_1424": "Biblioteca padrão", "Default_library_for_target_0_1425": "Biblioteca padrão para o destino '{0}'", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "As definições dos seguintes identificadores estão em conflito com as de outro arquivo: {0}", "Delete_all_unused_declarations_95024": "Excluir to<PERSON> as declarações não usadas", "Delete_all_unused_imports_95147": "Excluir todas as importações não usadas", "Delete_all_unused_param_tags_95172": "<PERSON>c<PERSON>ir todas as marcas '@param' não usadas", "Delete_the_outputs_of_all_projects_6365": "Excluir as sa<PERSON><PERSON> de todos os projetos.", "Delete_unused_param_tag_0_95171": "Excluir a marca '@param' não usada '{0}'", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Preterido] Use '--jsxFactory' no lugar. Especifique o objeto invocado para createElement ao direcionar uma emissão de JSX 'react'", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Preterido] Use '--outFile' no lugar. Concatene e emita uma saída para um arquivo único", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Preterido] Use '--skipLibCheck' no lugar. Ignore a verificação de tipo dos arquivos de declaração de biblioteca padrão.", "Deprecated_setting_Use_outFile_instead_6677": "Configuração preterida. Use 'outFile' em vez disso.", "Did_you_forget_to_use_await_2773": "Você esqueceu de usar 'await'?", "Did_you_mean_0_1369": "Você quis dizer '{0}'?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "Você quis dizer que '{0}' deve ser restrito ao tipo 'new (...args: any[]) => {1}'?", "Did_you_mean_to_call_this_expression_6212": "Você quis chamar esta expressão?", "Did_you_mean_to_mark_this_function_as_async_1356": "Você quis marcar esta função como 'async'?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Você quis usar ':'? '=' só pode estar após um nome de propriedade quando o literal de objeto contentor faz parte de um padrão de desestruturação.", "Did_you_mean_to_use_new_with_this_expression_6213": "Você quis usar 'new' com essa expressão?", "Digit_expected_1124": "<PERSON><PERSON><PERSON><PERSON> esperado.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "O diretório '{0}' não existe; ignorando to<PERSON> as pesquisas nele.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "O diretório '{0}' não contém o escopo package.json. As importações não resolverão.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Desabilitar a adição de diretivas 'use strict' em arquivos JavaScript emitidos.", "Disable_checking_for_this_file_90018": "Desabilitar a verificação para esse arquivo", "Disable_emitting_comments_6688": "Desabilitar comentários de emissão.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Desabilite as declarações de emissão que têm '@internal' em seus comentários JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Desabilitar a emissão de arquivos de uma compilação.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Desabilitar a emissão de arquivos se forem reportados erros de verificação de tipo.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Desabilitar a exclusão de declarações 'enum const' no código gerado.", "Disable_error_reporting_for_unreachable_code_6603": "Desabilitar o relatório de erros para código inacessível.", "Disable_error_reporting_for_unused_labels_6604": "Desabilitar o relatório de erros para rótulos não utilizados.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "Desative a verificação completa do tipo (somente erros críticos de análise e emissão serão relatados).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Desabilitar funções auxiliares personalizadas como '__extends' nas saídas compiladas.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Desabilitar a inclusão de qualquer arquivo de biblioteca, incluindo o padrão lib.d.ts.", "Disable_loading_referenced_projects_6235": "Desabilite o carregamento de projetos referenciados.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Desabilitar arquivos de origem de referência em vez de arquivos de declaração ao referenciar projetos compostos.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Desabilitar relatório de excesso de erros de propriedade durante a criação de literais de objeto.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Desabilitar a resolução de symlinks para seus realpath. Isso se correlaciona com o mesmo sinalizador no nó.", "Disable_size_limitations_on_JavaScript_projects_6162": "Desabilitar as limitações de tamanho nos projetos JavaScript.", "Disable_solution_searching_for_this_project_6224": "Desabilite a pesquisa de solução deste projeto.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Desabilitar verificação estrita de assinaturas genéricas em tipos de função.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Desabilitar a aquisição de tipo para projetos JavaScript", "Disable_truncating_types_in_error_messages_6663": "Desabilitar truncamento de tipos em mensagens de erro.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Desabilite o uso de arquivos de origem em vez de arquivos de declaração de projetos referenciados.", "Disable_wiping_the_console_in_watch_mode_6684": "Desabilitar a limpeza do console no modo de inspeção.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Desabilita a inferência para a aquisição de tipo examinando filenames em um projeto.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Não permitir 'importar', 'necessário ou' <reference> de expandir o número de arquivos que TypeScript deve adicionar a um projeto.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Não permitir referências com maiúsculas de minúsculas inconsistentes no mesmo arquivo.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "<PERSON><PERSON> adicionar as referências de barra tripla nem os módulos importados à lista de arquivos compilados.", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "Não permitir construções de tempo de execução que não fazem parte de ECMAScript.", "Do_not_emit_comments_to_output_6009": "Não emita comentários para a saída.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Não emita declarações de código que contenham uma anotação '@internal'.", "Do_not_emit_outputs_6010": "Não emita saídas.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Não emita saídas se erros forem reportados.", "Do_not_emit_use_strict_directives_in_module_output_6112": "Não emita diretivas 'use strict' na saída de módulo.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Não apague declarações const enum no código gerado.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Não gerar funções auxiliares personalizadas como '__extends' nas saídas compiladas.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Não incluir o arquivo de biblioteca padrão (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "Não relate erros sobre código inacessível.", "Do_not_report_errors_on_unused_labels_6074": "Não relate erros sobre rótulos não utilizados.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Não resolver o real caminho de symlinks.", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "Não transforme ou elimine quaisquer importações ou exportações não marcadas como somente tipo, garantindo que sejam gravadas no formato do arquivo de saída com base na configuração 'module'.", "Do_not_truncate_error_messages_6165": "<PERSON><PERSON> trun<PERSON> as mensa<PERSON> de erro.", "Duplicate_function_implementation_2393": "Implementação de função duplicada.", "Duplicate_identifier_0_2300": "Identificador '{0}' duplicado.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Identificador duplicado '{0}'. O compilador reserva o nome '{1}' no escopo de nível superior de um módulo.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Duplicar o identificador '{0}'. O compilador reserva o nome '{1}' em escopo de alto nível de um módulo que contém funções assíncronas.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Identificador duplicado '{0}'. O compilador reserva o nome '{1}' ao emitir 'super' referências em inicializadores estáticos.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Identificador '{0}' duplicado. O compilador usa a declaração '{1}' para dar suporte a funções assíncronas.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Identificador duplicado '{0}'. Os elementos estáticos e de instância não podem compartilhar o mesmo nome privado.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Identificador 'arguments' duplicado. O compilador usa 'arguments' para inicializar os parâmetros rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Identificador duplicado '_newTarget'. O compilador usa a declaração de variável '_newTarget' para capturar a referência de metapropriedade 'new.target'.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Identificador '_this' duplicado. O compilador usa a declaração de variável '_this' para capturar a referência 'this'.", "Duplicate_index_signature_for_type_0_2374": "Assinatura de índice duplicada para o tipo '{0}'.", "Duplicate_label_0_1114": "<PERSON><PERSON><PERSON><PERSON> '{0}' duplicado.", "Duplicate_property_0_2718": "Propriedade '{0}' duplicada.", "Duplicate_regular_expression_flag_1500": "Duplicar o sinalizador de expressão regular.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "O especificador da importação dinâmica deve ser do tipo 'string', mas aqui tem o tipo '{0}'.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Só há suporte para importações dinâmicas quando o sinalizador '--module' está definido como 'es2020', 'es2022', 'esnext', 'commonjs', 'amd', 'system', 'umd', 'node16', 'node18' ou 'nodenext'.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "As importações dinâmicas só podem aceitar um especificador de módulo e um conjunto opcional de atributos como argumentos", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "As importações dinâmicas só darão suporte a um segundo argumento quando a opção '--module' estiver definida como 'esnext', 'node16', 'node18', 'nodenext' ou 'preserve'.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "A sintaxe ESM não é permitida em um módulo CommonJS quando 'module' estiver definido como 'preserve'.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "A sintaxe ESM não é permitida em um módulo CommonJS quando a opção \"verbatimModuleSyntax\" estiver habilitada.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "Cada declaração de '{0}.{1}' difere em seu valor, onde '{2}' era esperado, mas '{3}' foi fornecido.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Cada membro do tipo de união '{0}' tem assinaturas de constructo, mas nenhuma dessas assinaturas é compatível entre si.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Cada membro do tipo de união '{0}' tem assinaturas, mas nenhuma dessas assinaturas é compatível entre si.", "Editor_Support_6249": "Suporte do Editor", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "O elemento implicitamente tem um tipo 'any' porque a expressão do tipo '{0}' não pode ser usada para o tipo de índice '{1}'.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "O elemento implicitamente tem um tipo 'any' porque a expressão de índice não é do tipo 'number'.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "O elemento tem, implicitamente, 'qualquer' tipo, pois o tipo '{0}' não tem assinatura de índice.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "O elemento implicitamente tem um tipo 'any' porque o tipo '{0}' não tem assinatura de índice. Você quis dizer chamada de '{1}'?", "Emit_6246": "<PERSON><PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Emitir os campos de classe ECMAScript-standard-compliant.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Emitir uma Marca de Ordem de Byte (BOM) UTF-8 no início dos arquivos de saída.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Emitir um arquivo único com os mapas de origem em vez de arquivos separados.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Emitir um perfil de CPU V8 da execução do compilador para depuração.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Emitir JavaScript adicional para facilitar o suporte à importação de módulos CommonJS. Isso habilita 'allowSyntheticDefaultImports' para compatibilidade de tipo.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Emita campos de classe com Definir em vez de Configurar.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Emitir metadados de tipo design para declarações decoradas nos arquivos de origem.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Emitir um JavaScript mais compatível, mas detalhado e menos eficaz para iteração.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Emitir a origem ao lado dos sourcemaps em um arquivo único; a definição requer '--inlineSourceMap' ou '--sourceMap'.", "Enable_all_strict_type_checking_options_6180": "Habilitar to<PERSON> as opções estritas de verificação de tipo.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Habilitar a cor e a formatação na saída do TypeScript para tornar os erros do compilador mais fáceis de ler.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Habilitar restrições que permitem que um projeto TypeScript seja usado com referências do projeto.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Habilitar o relatório de erros para codepaths que não retornam explicitamente uma função.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Habilitar o relatório de erros para expressões e declarações com um tipo 'any' implícito.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Habilitar o relatório de erros para casos fallthrough no parâmetro relatório.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Habilitar o relatório de erros em arquivos JavaScript verificados por tipo.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Habilitar relatório de erros quando as variáveis locais não forem lidas.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Habilitar relatório de erros quando 'this' for fornecido o tipo 'any'.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "Habilite o suporte experimental para decoradores experimentais herdados.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "Permite a importação de arquivos com qualquer extensão, desde que um arquivo de declaração esteja presente.", "Enable_importing_json_files_6689": "Habilitar importação de arquivos .json.", "Enable_lib_replacement_6808": "Habilitar substituição de biblioteca.", "Enable_project_compilation_6302": "Habilitar a compilação do projeto", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Habilite os métodos estritos 'bind', 'call' e 'apply' em funções.", "Enable_strict_checking_of_function_types_6186": "Habilitar verificação estrita de tipos de função.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Habilite a verificação estrita de inicialização de propriedade nas classes.", "Enable_strict_null_checks_6113": "Habilite verificações nulas estritas.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Habilitar a opção 'experimentalDecorators' no arquivo de configuração", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Habilitar o sinalizador '--jsx' no arquivo de configuração", "Enable_tracing_of_the_name_resolution_process_6085": "Habilite o rastreio do processo de resolução de nome.", "Enable_verbose_logging_6713": "Habilite o registro em log detalhado.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Permite emissão de interoperabilidade entre CommonJS e Módulos ES através da criação de objetos de namespace para todas as importações. Implica em 'allowSyntheticDefaultImports'.", "Enables_experimental_support_for_ES7_decorators_6065": "Habilita o suporte experimental para decoradores ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Habilita o suporte experimental para a emissão de tipo de metadados para decoradores.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Aplicar o uso de acessadores indexados para chaves declaradas usando um tipo indexado.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Verifique se os membros de substituição em classes derivadas estão marcados com um modificador de ignorar.", "Ensure_that_casing_is_correct_in_imports_6637": "Certifique-se de que a capitalização esteja correta nas importações.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Certifique-se que cada arquivo pode ser convertido em segurança sem depender de outras importações.", "Ensure_use_strict_is_always_emitted_6605": "Certifique-se de que 'use strict' seja sempre emitido.", "Entering_conditional_exports_6413": "Inserção de exportações condicionais.", "Entry_point_for_implicit_type_library_0_1420": "Ponto de entrada para a biblioteca de tipos implícita '{0}'", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Ponto de entrada para a biblioteca de tipos implícita '{0}' com packageId '{1}'", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Ponto de entrada da biblioteca de tipos '{0}' especificado em compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Ponto de entrada da biblioteca de tipos '{0}' especificado em compilerOptions com packageId '{1}'", "Enum_0_used_before_its_declaration_2450": "A enumeração '{0}' usada antes de sua declaração.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "As declarações enum só podem ser mescladas com namespaces ou com outras declarações enum.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Declarações de enumeração devem ser const ou não const.", "Enum_member_expected_1132": "Membro de enumeração esperado.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "O membro Enumeração que segue um membro numérico não literal deve ter um inicializador quando 'isolatedModules' estiver habilitado.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "Os inicializadores de membros de Enumeração devem ser computáveis sem referências a símbolos externos com --isolatedDeclarations.", "Enum_member_must_have_initializer_1061": "O membro de enumeração deve ter um inicializador.", "Enum_name_cannot_be_0_2431": "O nome de enumeração não pode ser '{0}'.", "Errors_Files_6041": "Arquivos de  Erros", "Escape_sequence_0_is_not_allowed_1488": "A sequência de escape '{0}' não é permitida.", "Examples_Colon_0_6026": "Exemplos: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "Complexidade excessiva ao comparar os tipos '{0}' e '{1}'.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Profundidade da pilha excessiva ao comparar tipos '{0}' e '{1}'.", "Exiting_conditional_exports_6416": "Saída das exportações condicionais.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Espera-se {0}-{1} argumentos de tipo; forneça esses recursos com uma marca \"@extends\".", "Expected_0_arguments_but_got_1_2554": "{0} argumentos eram esperados, mas {1} foram obtidos.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "{0} argumentos eram esperados, mas foram obtidos {1}. Você esqueceu de incluir 'void' no argumento de tipo para 'Promise'?", "Expected_0_type_arguments_but_got_1_2558": "{0} argumentos de tipo eram esperados, mas {1} foram obtidos.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Espera-se {0} argumentos de tipo; forneça esses recursos com uma marca \"@extends\".", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Esperava 1 argumento, mas obteve 0. 'new Promise()' precisa de uma dica JSDoc para produzir um 'resolver' que pode ser chamado sem argumentos.", "Expected_a_Unicode_property_name_1523": "Esperado um nome de propriedade Unicode.", "Expected_a_Unicode_property_name_or_value_1527": "Espera-se um nome ou valor de propriedade Unicode.", "Expected_a_Unicode_property_value_1525": "Espera-se um valor da propriedade Unicode.", "Expected_a_capturing_group_name_1514": "Esperado um nome de grupo de captura.", "Expected_a_class_set_operand_1520": "Espera-se um operando de conjunto de classes.", "Expected_at_least_0_arguments_but_got_1_2555": "<PERSON><PERSON> menos {0} argumentos eram esperados, mas {1} foram obtidos.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Marca de fechamento de JSX correspondente esperada para '{0}'.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Marca de fechamento correspondente esperada para fragmento JSX.", "Expected_for_property_initializer_1442": "Esperado '=' para inicializador de propriedade.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "O tipo esperado do campo '{0}' em 'package.json' como '{1}' obteve '{2}'.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Tipo de resolução de módulo especificado explicitamente: '{0}'.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "A exponenciação não pode ser executada nos valores 'bigint', a menos que a opção 'target' esteja definida como 'es2016' ou posterior.", "Export_0_from_module_1_90059": "Exportar '{0}' do módulo '{1}'", "Export_all_referenced_locals_90060": "Exportar todos os locais referenciados", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Não é possível usar a atribuição de exportação durante o direcionamento para módulos de ECMAScript. Use a 'exportação padrão' ou outro formato de módulo em vez disso.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "A atribuição de exportação não tem suporte quando o sinalizador '--module' é 'system'.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Exportar conflitos de declaração com declaração exportada de '{0}'.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "As declarações de exportação não são permitidas em um namespace.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "O especificador de exportação '{0}' não existe no escopo package.json no caminho '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "O alias de tipo exportado '{0}' tem ou está usando o nome particular '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "O alias do tipo exportado '{0}' tem ou está usando o nome privado '{1}' do módulo {2}.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "A variável exportada '{0}' tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeada.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "A variável exportada '{0}' tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "A variável exportada '{0}' tem ou está usando o nome particular '{1}'.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Exportações e designações de exportações não são permitidas em acréscimos de módulo.", "Expression_expected_1109": "Expressão esperada.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "A expressão deve ser colocada entre parênteses para ser usada como um decorador.", "Expression_or_comma_expected_1137": "Expressão ou vírgula esperada.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "A expressão produz um tipo de tupla grande demais para ser representada.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "A expressão produz um tipo de união muito complexo para representar.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "A expressão é resolvida como '_super', que o compilador utiliza para capturar a referência da classe base.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "A expressão é resolvida para a declaração de variável '_newTarget' que o compilador usa para capturar a referência de metapropriedade 'new.target'.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "A expressão é resolvida como a declaração de variável '_this' que o compilador utiliza para capturar a referência 'this'.", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "O tipo de expressão não pode ser inferido com --isolatedDeclarations.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "A cláusula Extends não pode conter uma expressão com --isolatedDeclarations.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "A cláusula extends para o tipo inferido '{0}' tem ou está usando o nome particular '{1}'.", "Extract_base_class_to_variable_90064": "Extrair classe base para variável", "Extract_binding_expressions_to_variable_90066": "Extrair expressões de associação para variável", "Extract_constant_95006": "Extrair constante", "Extract_default_export_to_variable_90065": "Extrair exportação padrão para variável", "Extract_function_95005": "Extrair função", "Extract_to_0_in_1_95004": "Extrair para {0} em {1}", "Extract_to_0_in_1_scope_95008": "Extrair para {0} no escopo {1}", "Extract_to_0_in_enclosing_scope_95007": "Extrair para {0} no escopo de delimitação", "Extract_to_interface_95090": "Extrair para interface", "Extract_to_type_alias_95078": "Extrair para alias de tipo", "Extract_to_typedef_95079": "Extrair para typedef", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "Extrair para variável e substituir por '{0} as typeof {0}'", "Extract_type_95077": "Tipo de extração", "FILE_6035": "ARQUIVO", "FILE_OR_DIRECTORY_6040": "ARQUIVO OU DIRETÓRIO", "Failed_to_find_peerDependency_0_6283": "Falha ao localizar peerDependency '{0}'.", "Failed_to_resolve_under_condition_0_6415": "Falha ao resolver na condição '{0}'.", "Fallthrough_case_in_switch_7029": "Caso de fallthrough no comutador.", "File_0_does_not_exist_6096": "O arquivo '{0}' não existe.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "O arquivo '{0}' não existe de acordo com as pesquisas anteriores em cache.", "File_0_exists_according_to_earlier_cached_lookups_6239": "O arquivo '{0}' existe de acordo com as pesquisas anteriores em cache.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "O arquivo '{0}' existe; use-o como um resultado de resolução de nome.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "O arquivo '{0}' tem uma extensão sem suporte. As únicas extensões com suporte são {1}.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "O arquivo '{0}' é um arquivo JavaScript. Você quis habilitar a opção 'allowJs'?", "File_0_is_not_a_module_2306": "O arquivo '{0}' não é um módulo.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "O arquivo '{0}' não está na lista de arquivos de projeto '{1}'. Os projetos devem listar todos os arquivos ou usar um padrão 'include'.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "O arquivo '{0}' não está em 'rootDir' '{1}'. Espera-se que 'rootDir' contenha todos os arquivos de origem.", "File_0_not_found_6053": "Arquivo '{0}' não encontrado.", "File_Management_6245": "Gerenciamento de Arquivos", "File_appears_to_be_binary_1490": "O arquivo parece ser binário.", "File_change_detected_Starting_incremental_compilation_6032": "Alteração do arquivo detectada. Iniciando compilação incremental...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "O arquivo é um módulo CommonJS porque '{0}' não tem o campo \"type\"", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "O arquivo é o módulo CommonJS porque '{0}' tem o campo \"type\" cujo valor não é \"module\"", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "O arquivo é um módulo CommonJS porque 'package.json' não foi encontrado", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "O arquivo é o módulo ECMAScript porque '{0}' tem o campo \"type\" com o valor \"module\"", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "O arquivo é um módulo CommonJS; ele pode ser convertido em um módulo ES.", "File_is_default_library_for_target_specified_here_1426": "O arquivo é a biblioteca padrão para o destino especificado aqui.", "File_is_entry_point_of_type_library_specified_here_1419": "O arquivo é o ponto de entrada da biblioteca de tipos especificada aqui.", "File_is_included_via_import_here_1399": "O arquivo é incluído via importação aqui.", "File_is_included_via_library_reference_here_1406": "O arquivo é incluído via referência de biblioteca aqui.", "File_is_included_via_reference_here_1401": "O arquivo é incluído via referência aqui.", "File_is_included_via_type_library_reference_here_1404": "O arquivo é incluído via referência de biblioteca de tipos.", "File_is_library_specified_here_1423": "O arquivo é a biblioteca especificada aqui.", "File_is_matched_by_files_list_specified_here_1410": "O arquivo corresponde à lista 'files' especificada aqui.", "File_is_matched_by_include_pattern_specified_here_1408": "O arquivo corresponde ao padrão de inclusão especificado aqui.", "File_is_output_from_referenced_project_specified_here_1413": "O arquivo é a saída do projeto referenciado especificado aqui.", "File_is_output_of_project_reference_source_0_1428": "O arquivo é a saída da origem de referência do projeto '{0}'", "File_is_source_from_referenced_project_specified_here_1416": "O arquivo é a origem do projeto referenciado especificado aqui.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "O nome do arquivo '{0}' difere do nome de arquivo '{1}' já incluído somente em maiúsculas e minúsculas.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "O nome de arquivo '{0}' tem uma extensão '{1}' – procurando '{2}' em vez disso.", "File_name_0_has_a_1_extension_stripping_it_6132": "O nome do arquivo '{0}' tem uma extensão '{1}' – remoção.", "File_redirects_to_file_0_1429": "O arquivo redireciona para o arquivo '{0}'", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "A especificação de arquivo não pode conter um diretório pai ('..') que aparece após um curinga de diretório recursivo ('**'): '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "A especificação de arquivo não pode terminar em um curinga do diretório recursivo ('**'): '{0}'.", "Filters_results_from_the_include_option_6627": "Filtra os resultados da opção 'include'.", "Fix_all_detected_spelling_errors_95026": "Corrigir todos os erros de ortografia detectados", "Fix_all_expressions_possibly_missing_await_95085": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as expressões possivelmente com 'await' ausente", "Fix_all_implicit_this_errors_95107": "Corrigir todos os erros de 'this' implícitos", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Corrigir todo tipo de retorno incorreto de uma função assíncrona", "Fix_all_with_type_only_imports_95182": "Corrigir tudo com importações somente de tipo", "Found_0_errors_6217": "Encontrados {0} erros.", "Found_0_errors_Watching_for_file_changes_6194": "{0} erros encontrados. Monitorando alterações de arquivo.", "Found_0_errors_in_1_files_6261": "Foram encontrados {0} erros em {1} arquivos.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Foram encontrados {0} erros no mesmo arquivo, começando em: {1}", "Found_1_error_6216": "Encontrado 1 erro.", "Found_1_error_Watching_for_file_changes_6193": "Um erro encontrado. Monitorando alterações de arquivo.", "Found_1_error_in_0_6259": "Encontrado 1 erro em {0}", "Found_package_json_at_0_6099": "'package.json' encontrado em '{0}'.", "Found_peerDependency_0_with_1_version_6282": "Foi encontrada peerDependency '{0}' com a versão '{1}'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "Declarações de função não são permitidas dentro de blocos em modo estrito quando o objetivo é 'ES5'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "Declarações de função não são permitidas dentro de blocos em modo estrito quando o objetivo é 'ES5'. Definições de classe estão automaticamente em modo estrito.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "Declarações de função não são permitidas dentro de blocos em modo estrito quando o objetivo é 'ES5'. Módulos estão automaticamente em modo estrito.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "A expressão de função, que não tem a anotação de tipo de retorno, implicitamente tem um tipo de retorno '{0}'.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "A implementação da função está ausente ou não está imediatamente depois da declaração.", "Function_implementation_name_must_be_0_2389": "O nome da implementação de função deve ser '{0}'.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "A função tem o tipo de retorno 'any', de forma implícita, porque ela não tem uma anotação de tipo de retorno e é referenciada direta ou indiretamente em uma das suas expressões de retorno.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "A função não tem a instrução return final e o tipo de retorno não inclui 'undefined'.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "A função deve ter uma anotação de tipo de retorno explícita com --isolatedDeclarations.", "Function_not_implemented_95159": "Função não implementada.", "Function_overload_must_be_static_2387": "A sobrecarga de função deve ser estática.", "Function_overload_must_not_be_static_2388": "A sobrecarga de função não deve ser estática.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "A notação de tipo de função precisa estar entre parênteses quando usada em um tipo de união.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "A notação de tipo de função precisa estar entre parênteses quando usada em um tipo de interseção.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "O tipo de função, que não tem a anotação de tipo de retorno, implicitamente tem um tipo de retorno '{0}'.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "A função com corpos só podem ser mescladas com classes que são ambientes.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Gerar arquivos d.ts de arquivos TypeScript e JavaScript em seu projeto.", "Generate_get_and_set_accessors_95046": "Gerar acessadores 'get' e 'set'", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "<PERSON><PERSON><PERSON> os acessadores 'get' e 'set' para todas as propriedades de substituição", "Generates_a_CPU_profile_6223": "Gera um perfil de CPU.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Gera um sourcemap para cada arquivo '.d.ts' correspondente.", "Generates_an_event_trace_and_a_list_of_types_6237": "Gera um rastreamento de eventos e uma lista de tipos.", "Generates_corresponding_d_ts_file_6002": "Gera o arquivo '.d.ts' correspondente.", "Generates_corresponding_map_file_6043": "Gera o arquivo '.map' correspondente.", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "O gerador tem implicitamente o tipo de rendimento \"{0}\". Considere fornecer uma anotação de tipo de retorno.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Os geradores não são permitidos em um contexto de ambiente.", "Generic_type_0_requires_1_type_argument_s_2314": "O tipo genérico '{0}' exige {1} argumento(s) de tipo.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "O tipo genérico '{0}' exige argumentos de tipo entre {1} e {2}.", "Global_module_exports_may_only_appear_at_top_level_1316": "As exportações de módulo global podem somente aparecer no nível superior.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "As exportações de módulo global podem somente aparecer em arquivos de declaração.", "Global_module_exports_may_only_appear_in_module_files_1314": "As exportações de módulo global podem somente aparecer em arquivos de módulo.", "Global_type_0_must_be_a_class_or_interface_type_2316": "O tipo global '{0}' deve ser um tipo de classe ou interface.", "Global_type_0_must_have_1_type_parameter_s_2317": "O tipo global '{0}' deve ter {1} parâmetro(s) de tipo.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Faça com que as recompilações em '--incremental' e '--watch' presumam que as alterações dentro de um arquivo só afetarão os arquivos que dependem diretamente dele.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Ter recompilações em projetos que usam os modos 'incremental' e 'inspeção' pressupõem que as alterações em um arquivo afetarão apenas os arquivos diretamente dependendo dele.", "Hexadecimal_digit_expected_1125": "Dígito hexadecimal esperado.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Um identificador é esperado. '{0}' é uma palavra reservada no nível superior de um módulo.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Identificador esperado. '{0}' é uma palavra reservada no modo estrito.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Identificador esperado. '{0}' é uma palavra reservada no modo estrito. Definições de classe estão automaticamente no modo estrito.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Identificador esperado. '{0}' é uma palavra reservada em modo estrito. Os módulos ficam automaticamente em modo estrito.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Identificador esperado. '{0}' é uma palavra reservada que não pode ser usada aqui.", "Identifier_expected_1003": "Identificador esperado.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Identificador esperado. '__esModule' é reservado como um marcador exportado ao transformar os módulos ECMAScript.", "Identifier_or_string_literal_expected_1478": "Identificador ou literal de cadeia de caracteres esperado.", "Identifier_string_literal_or_number_literal_expected_1496": "Identific<PERSON>, literal de cadeia de caracteres ou literal de número esperado.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Se o pacote '{0}' realmente expõe este módulo, considere enviar uma solicitação de pull para corrigir 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}'", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Se o pacote '{0}' realmente expõe este módulo, tente adicionar um novo arquivo de declaração (.d.ts) contendo o módulo `declare '{1}';`", "Ignore_this_error_message_90019": "Ignorar essa mensagem de erro", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignorar o tsconfig.json, compila os arquivos especificados com as opções do compilador padrão.", "Implement_all_inherited_abstract_classes_95040": "Implementar todas as classes abstratas herdadas", "Implement_all_unimplemented_interfaces_95032": "Implementar todas as interfaces não implementadas", "Implement_inherited_abstract_class_90007": "Implementar classe abstrata herdada", "Implement_interface_0_90006": "Implementar a interface '{0}'", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "A cláusula implements da classe exportada '{0}' tem ou está usando o nome particular '{1}'.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "A conversão implícita de um 'symbol' em ' string' falhará em runtime. Considere o encapsulamento desta expressão em 'String(...)'.", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "A importação '{0}' está em conflito com o valor global usado neste arquivo, portanto, deve ser declarada com uma importação somente de tipo quando 'isolatedModules' está habilitado.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "A importação '{0}' está em conflito com o valor local, portanto, deve ser declarada com uma importação somente de tipo quando 'isolatedModules' está habilitado.", "Import_0_from_1_90013": "Importar '{0}' de \"{1}\"", "Import_assertion_values_must_be_string_literal_expressions_2837": "Os valores de asserção de importação devem ser expressões literais de cadeias de caracteres.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "As declarações de importação não são permitidas em declarações que compilam para chamadas 'require' do commonjs.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "As declarações de importação são suportadas apenas quando a opção '--module' estiver definida como 'esnext', 'node18', 'nodenext' ou 'preserve'.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "As afirmações de importação não podem ser usadas com importações ou exportações somente de tipo.", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "As asserções de importação foram substituídas por atributos de importação. Use 'with' em vez de 'assert'.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Não é possível usar a atribuição de importação durante o direcionamento para módulos de ECMAScript. Use 'importar * como ns de \"mod\"', 'importar {a} de \"mod\"', 'importar d de \"mod\"' ou outro formato de módulo em vez disso.", "Import_attribute_values_must_be_string_literal_expressions_2858": "Os valores do atributo de importação devem ser expressões literais de cadeias de caracteres.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "Atributos de importação não são permitidos em instruções que são compiladas para chamadas 'require' do CommonJS.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "Os atributos de importação só têm suporte quando a opção '--module' estiver definida como 'esnext', 'node18', 'nodenext' ou 'preserve'.", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "Os atributos de importação não podem ser usadas com importações ou exportações somente de tipo.", "Import_declaration_0_is_using_private_name_1_4000": "A declaração da importação '{0}' está usando o nome particular '{1}'.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "A declaração da importação está em conflito com a declaração local '{0}'.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "As declarações de importação em um namespace não podem fazer referência a um módulo.", "Import_emit_helpers_from_tslib_6139": "Importar auxiliares de emissão de 'tslib'.", "Import_may_be_converted_to_a_default_import_80003": "A importação pode ser convertida em uma importação padrão.", "Import_name_cannot_be_0_2438": "O nome da importação não pode ser '{0}'.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "A declaração de importação e exportação em uma declaração de módulo de ambiente não pode fazer referência ao módulo por meio do nome do módulo relativo.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "O especificador de importação '{0}' não existe no escopo package.json no caminho '{1}'.", "Imported_via_0_from_file_1_1393": "Importado via {0} do arquivo '{1}'", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Importado via {0} do arquivo '{1}' para importar 'importHelpers' conforme especificado em compilerOptions", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Importado via {0} do arquivo '{1}' para importar as funções de fábrica 'jsx' e 'jsxs'", "Imported_via_0_from_file_1_with_packageId_2_1394": "Importado via {0} do arquivo '{1}' com packageId '{2}'", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Importado via {0} do arquivo '{1}' com packageId '{2}' para importar 'importHelpers' conforme especificado em compilerOptions", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Importado via {0} do arquivo '{1}' com packageId '{2}' para importar as funções de fábrica 'jsx' e 'jsxs'", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "A importação de um arquivo JSON para um módulo ECMAScript requer um 'type: \"json\"' quando o atributo de importação \"module\" estiver definido como \"{0}\".", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Importações não são permitidas em acréscimos de módulo. Considere movê-las para o módulo externo delimitador.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Em declarações de enumeração de ambiente, o inicializador de membro deve ser uma expressão de constante.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "Em uma enumeração com várias declarações, somente uma declaração pode omitir um inicializador para o primeiro elemento de enumeração.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Incluir uma lista de arquivos. <PERSON><PERSON> n<PERSON> oferece suporte a padrões glob, ao contrário de 'include'.", "Include_modules_imported_with_json_extension_6197": "Incluir módulos importados com a extensão '.json'", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Incluir o código-fonte no sourcemaps dentro do JavaScript emitido.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Incluir arquivos sourcemap dentro do JavaScript emitido.", "Includes_imports_of_types_referenced_by_0_90054": "Inclui importações de tipos referenciados por '{0}'", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Incluir --watch, -w come<PERSON><PERSON><PERSON> a observar o projeto atual para as alterações do arquivo. Uma vez definido, você pode configurar o modo de inspeção com:", "Incomplete_quantifier_Digit_expected_1505": "Quantificador incompleto. Dígito esperado.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "A assinatura do índice para o tipo '{0}' está ausente no tipo '{1}'.", "Index_signature_in_type_0_only_permits_reading_2542": "Assinatura de índice no tipo '{0}' permite somente leitura.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "<PERSON><PERSON> as declarações individuais na declaração mesclada '{0}' devem ser exportadas ou ficar no local.", "Infer_all_types_from_usage_95023": "<PERSON><PERSON>ir todos os tipos de uso", "Infer_function_return_type_95148": "Inferir o tipo de retorno da função", "Infer_parameter_types_from_usage_95012": "Inferir tipos de parâmetro pelo uso", "Infer_this_type_of_0_from_usage_95080": "Inferir tipo 'this' de '{0}' do uso", "Infer_type_of_0_from_usage_95011": "Inferir tipo de '{0}' pelo uso", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "Não há suporte para inferência de expressões de classe com --isolatedDeclarations.", "Initialize_property_0_in_the_constructor_90020": "Inicializar a propriedade '{0}' no construtor", "Initialize_static_property_0_90021": "In<PERSON>ali<PERSON> a propriedade estática '{0}'", "Initializer_for_property_0_2811": "Inicializador para a propriedade '{0}'", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "O inicializador da variável de membro de instância '{0}' não pode referenciar o identificador '{1}' declarado no construtor.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Inicializadores não são permitidos em contextos de ambiente.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Inicializa um projeto TypeScript e cria um arquivo tsconfig.json.", "Inline_variable_95184": "Variável embutida", "Insert_command_line_options_and_files_from_a_file_6030": "Inserir opções e arquivos de linha de comando de um arquivo.", "Install_0_95014": "Instalar '{0}'", "Install_all_missing_types_packages_95033": "Instalar todos os pacotes de tipos ausentes", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "A interface '{0}' não pode estender os tipos '{1}' e '{2}' simultaneamente.", "Interface_0_incorrectly_extends_interface_1_2430": "A interface '{0}' estende incorretamente a interface '{1}'.", "Interface_declaration_cannot_have_implements_clause_1176": "A declaração de interface não pode ter a cláusula 'implements'.", "Interface_must_be_given_a_name_1438": "A interface deve receber um nome.", "Interface_name_cannot_be_0_2427": "O nome da interface não pode ser '{0}'.", "Interop_Constraints_6252": "Restrições Interop", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interprete os tipos de propriedade opcionais conforme escritos, em vez de adicionar 'indefinido'.", "Invalid_character_1127": "Caractere inválido.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "O especificador de importação inválido '{0}' não tem resoluções possíveis.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Nome de módulo inválido no aumento. O módulo '{0}' resolve para um módulo não tipado em '{1}', que não pode ser aumentado.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Nome de módulo inválido em acréscimo, o módulo '{0}' não pôde ser encontrado.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Cadeia opcional inválida da nova expressão. Você quis dizer chamar '{0}()'?", "Invalid_reference_directive_syntax_1084": "Sintaxe de diretiva 'reference' inválida.", "Invalid_syntax_in_decorator_1498": "Sintaxe inválida no decorador.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Uso inválido de “{0}”. Ele não pode ser usado dentro de um bloco estático de classe.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "<PERSON>o inválido de '{0}'. Os módulos ficam automaticamente em modo estrito.", "Invalid_use_of_0_in_strict_mode_1100": "<PERSON>o inválido de '{0}' no modo estrito.", "Invalid_value_for_ignoreDeprecations_5103": "<PERSON><PERSON> inválido para '--ignoreDeprecations'.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Valor inválido para 'jsxFactory'. '{0}' não é um identificador válido ou nome qualificado.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Valor inválido para 'jsxFragmentFactory'. '{0}' não é um identificador válido ou nome qualificado.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Valor inválido para '--reactNamespace'. '{0}' não é um identificador válido.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "É provável que não haja uma vírgula para separar essas duas expressões de modelo. Elas formam uma expressão de modelo marcada que não pode ser invocada.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Seu tipo de elemento '{0}' não é um elemento JSX válido.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Seu tipo de instância '{0}' não é um elemento JSX válido.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Seu tipo de retorno '{0}' não é um elemento JSX válido.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "Seu tipo '{0}' não é um elemento JSX válido.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "O '@{0} {1}' do JSDoc não corresponde à cláusula 'extends {2}'.", "JSDoc_0_is_not_attached_to_a_class_8022": "O '@{0}' do JSDoc não está anexado a uma classe.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' só pode aparecer no último parâmetro de uma assinatura.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "A marcação do JSDoc \"@param\" tem o nome \"{0}\", mas não há nenhum parâmetro com esse nome.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "A marca '@param' do JSDoc tem o nome '{0}', mas não há nenhum parâmetro com esse nome. Ela corresponderia a 'argumentos' se tivesse um tipo de matriz.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "Typedef JSDoc pode ser convertido em tipo TypeScript.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "A marca JSDoc \"@typedef\" deve ter uma anotação de tipo ou ser seguida pelas marcas \"@property\" or \"@member\".", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "Typedefs JSDoc podem ser convertidos em tipos TypeScript.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Os tipos de JSDoc podem ser usados somente dentro dos comentários de documentação.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Tipos JSDoc podem ser movidos para tipos TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Os atributos JSX só devem ser atribuídos a 'expressões' que não estejam vazias.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "O elemento JSX '{0}' não tem uma marcação de fechamento correspondente.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "A classe do elemento JSX não dá suporte a atributos porque não tem uma propriedade '{0}'.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "O elemento JSX implicitamente tem o tipo 'any' porque não há uma interface de 'JSX.{0}'.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "O elemento JSX implicitamente tem tipo 'any' porque o tipo global \"JSX.Element\" não existe.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "O tipo de elemento JSX '{0}' não tem nenhum constructo nem assinaturas de chamadas.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Elementos JSX não podem ter vários atributos com o mesmo nome.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Expressões JSX não podem usar o operador vírgula. Você quis escrever uma matriz?", "JSX_expressions_must_have_one_parent_element_2657": "As expressões JSX devem ter um elemento pai.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "O fragmento JSX não tem uma marcação de fechamento correspondente.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "As expressões de acesso da propriedade JSX não podem incluir nomes do namespace JSX", "JSX_spread_child_must_be_an_array_type_2609": "O filho do espalhamento JSX deve ser um tipo de matriz.", "JavaScript_Support_6247": "Suporte do JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "O destino do salto não pode ultrapassar o limite de função.", "KIND_6034": "TIPO", "Keywords_cannot_contain_escape_characters_1260": "As palavras-chave não podem conter caracteres de escape.", "LOCATION_6037": "LOCAL", "Language_and_Environment_6254": "Idioma e Ambiente", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "O operador antes da vírgula não é usado e não tem efeitos colaterais.", "Library_0_specified_in_compilerOptions_1422": "Biblioteca '{0}' especificada em compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Biblioteca referenciada via '{0}' do arquivo '{1}'", "Line_break_not_permitted_here_1142": "Quebra de linha não permitida aqui.", "Line_terminator_not_permitted_before_arrow_1200": "Terminador de linha não permitido antes de seta.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Lista de sufixos de nome de arquivo a serem pesquisadas ao resolver um módulo.", "List_of_folders_to_include_type_definitions_from_6161": "Lista de pastas das quais são incluídas as definições de tipo.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Listas das pastas raiz cujo conteúdo combinado representa a estrutura do projeto no tempo de execução.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Carregando '{0}' do diretório raiz '{1}', local candidato '{2}'.", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "Carregando o módulo '{0}' da pasta 'node_modules', tipo de arquivo de destino '{1}'.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "Carregando módulo como arquivo/pasta, local do módulo candidato '{0}', tipo de arquivo de destino {1}.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "A localidade deve estar no formato <language> ou <language>-<territory>. Por exemplo '{0}' ou '{1}'.", "Log_paths_used_during_the_moduleResolution_process_6706": "Caminhos de log usados durante o processo 'moduleResolution'.", "Longest_matching_prefix_for_0_is_1_6108": "O maior prefixo correspondente para '{0}' é '{1}'.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Pesquisando na pasta 'node_modules', local inicial '{0}'.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "<PERSON><PERSON> as chama<PERSON> 'super()' a primeira instrução nos respectivos construtores", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "Fazer com que o keyof retorne apenas cadeias de caracteres, números ou símbolos. Opção herdada.", "Make_super_call_the_first_statement_in_the_constructor_90002": "<PERSON><PERSON> a chamada 'super()' a primeira instrução no construtor", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "O tipo de objeto mapeado implicitamente tem um tipo de modelo 'any'.", "Mark_array_literal_as_const_90070": "Marcar literal de matriz como const", "Matched_0_condition_1_6403": "'{0}' correspondente à condição '{1}'.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Correspondido por padrão de inclusão padrão '**/*'", "Matched_by_include_pattern_0_in_1_1407": "Correspondência pelo padrão de inclusão '{0}' em '{1}'", "Member_0_implicitly_has_an_1_type_7008": "O membro '{0}' implicitamente tem um tipo '{1}'.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "O membro '{0}' implicitamente tem um tipo '{1}', mas um tipo melhor pode ser inferido do uso.", "Merge_conflict_marker_encountered_1185": "Marcador de conflito de mesclagem encontrado.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "A declaração mesclada '{0}' não pode conter uma declaração de exportação padrão. Considere adicionar uma declaração 'export default {0}' independente.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "A metapropriedade '{0}' só é permitida no corpo de uma declaração de função, expressão de função ou construtor.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "O método '{0}' não pode ter uma implementação, pois está marcado como abstrato.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "O método '{0}' da interface exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "O método '{0}' da interface exportada tem ou está usando o nome privado '{1}'.", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "O método deve ter uma anotação de tipo de retorno explícita com --isolatedDeclarations.", "Method_not_implemented_95158": "Método não implementado.", "Modifiers_cannot_appear_here_1184": "Modificadores não podem aparecer aqui.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "O módulo '{0}' só pode ser importado por padrão usando o sinalizador '{1}'", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "O módulo '{0}' não pode ser importado usando esta construção. O especificador resolve apenas para um módulo ES, que não pode ser importado com 'require'. Em vez disso, use uma importação ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "O módulo '{0}' declara '{1}' localmente, mas é exportado como '{2}'.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "O módulo '{0}' declara '{1}' localmente, mas não é exportado.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "O módulo '{0}' não faz referência a um tipo, mas é usado como um tipo aqui. Você quis dizer 'typeof import('{0}')'?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "O módulo '{0}' não faz referência a um valor, mas é usado como um valor aqui.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "O módulo {0} já exportou um membro denominado '{1}'. Considere reexportar explicitamente para resolver a ambiguidade.", "Module_0_has_no_default_export_1192": "O módulo '{0}' não tem padrão de exportação.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "O módulo '{0}' não tem exportação padrão. Você quis dizer 'importar { {1} } de {0}' em vez disso?", "Module_0_has_no_exported_member_1_2305": "O módulo '{0}' não tem nenhum membro exportado '{1}'.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "O módulo '{0}' não tem membro exportado '{1}'. Você quis dizer 'importar {1} de {0}' em vez disso?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "O módulo '{0}' está oculto por uma declaração de local com o mesmo nome.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "O módulo '{0}' usa 'export =' e não pode ser usado com 'export *'.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "O módulo '{0}' foi resolvido como módulo de ambiente declarado localmente no arquivo '{1}'.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "O módulo '{0}' foi resolvido para '{1}', mas '--allowArbitraryExtensions' não está definido.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "O módulo '{0}' foi resolvido para '{1}', mas '--jsx' não está configurado.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "O módulo '{0}' foi resolvido para '{1}', mas '--resolveJsonModule' não é usado.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Os nomes de declaração de módulo só podem usar ' ou “ cadeias de caracteres entre aspas.", "Module_name_0_matched_pattern_1_6092": "Nome do módulo '{0}', padr<PERSON> correspondido '{1}'.", "Module_name_0_was_not_resolved_6090": "======== Nome do módulo '{0}' não foi resolvido. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Nome do módulo '{0}' foi resolvido com sucesso '{1}'. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== O nome do módulo '{0}' foi resolvido com sucesso para '{1}' com a ID do Pacote '{2}'. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Resolução de tipo não foi especificado, usando '{0}'.", "Module_resolution_using_rootDirs_has_failed_6111": "Falha na resolução de módulo usando 'rootDirs'.", "Modules_6244": "<PERSON><PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Mover os modificadores de elemento de tupla rotulados para rótulos", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "Mova a expressão na exportação padrão para uma variável e adicione uma anotação de tipo a ela.", "Move_to_a_new_file_95049": "Mover para um novo arquivo", "Move_to_file_95178": "Mover para Arquivo", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Não são permitidos vários separadores numéricos consecutivos.", "Multiple_constructor_implementations_are_not_allowed_2392": "Não são permitidas várias implementações de construtor.", "NEWLINE_6061": "NEWLINE", "Name_is_not_valid_95136": "Nome inválido", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "Grupos de captura nomeados só estão disponíveis ao direcionar para 'ES2018' ou posterior.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "Grupos de captura nomeados com o mesmo nome devem ser mutuamente exclusivos entre si.", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "As importações nomeadas de um arquivo JSON para um módulo ECMAScript não são permitidas quando \"module\" é definido como \"{0}\".", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "As propriedades com nome '{0}' dos tipos '{1}' e '{2}' não são idênticas.", "Namespace_0_has_no_exported_member_1_2694": "O namespace '{0}' não tem o membro exportado '{1}'.", "Namespace_must_be_given_a_name_1437": "O Namespace deve receber um nome.", "Namespace_name_cannot_be_0_2819": "O nome do Namespace não pode ser '{0}'.", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "Namespaces não são permitidos em arquivos de script global quando '{0}' está habilitado. Se esse arquivo não se destina a ser um script global, defina 'moduleDetection' como 'force' ou adicione uma instrução 'export {}' vazia.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "Nem decoradores nem modificadores podem ser aplicados a parâmetros 'this'.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Nenhum construtor base tem o número especificado de argumentos de tipo.", "No_constituent_of_type_0_is_callable_2755": "Nenhum membro do tipo '{0}' pode ser chamado.", "No_constituent_of_type_0_is_constructable_2759": "Nenhum membro do tipo '{0}' é construído.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "Nenhuma assinatura de índice com um parâmetro do tipo '{0}' foi localizada no tipo '{1}'.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "Nenhuma entrada foi localizada no arquivo de configuração '{0}'. Os caminhos especificados foram 'incluir' '{1}' e 'excluir' '{2}'.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Não há mais suporte. Em versões iniciais, defina manualmente a codificação de texto para a leitura de arquivos.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "Nenhuma sobrecarga espera argumentos {0}, mas existem sobrecargas que esperam argumentos {1} ou {2}.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Nenhuma sobrecarga espera argumentos do tipo {0}, mas existem sobrecargas que esperam argumentos do tipo {1} ou {2}.", "No_overload_matches_this_call_2769": "Nenhuma sobrecarga corresponde a esta chamada.", "No_type_could_be_extracted_from_this_type_node_95134": "Não foi possível extrair nenhum tipo deste nó de tipo", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Não existe valor no escopo para a propriedade abreviada '{0}'. Declare um ou forneça um inicializador.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "A classe não abstrata '{0}' não implementa o membro abstrato herdado {1} da classe '{2}'.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "A classe não abstrata '{0}' não tem implementações para os seguintes membros de '{1}': {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "A classe não abstrata '{0}' não tem implementações para os seguintes membros de '{1}': {2} e mais {3}.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "A expressão da classe não abstrata não implementa o membro abstrato herdado '{0}' da classe '{1}'.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "Não há implementações de expressão de classe não abstrata para os seguintes membros de '{0}': {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "Não há implementações de expressão de classe não abstrata para os seguintes membros de '{0}': {1} e mais {2}.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "As declarações não nulas só podem ser usadas em arquivos TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Os caminhos não relativos não são permitidos quando a 'baseUrl' não está definida. Você esqueceu um './' à esquerda?", "Non_simple_parameter_declared_here_1348": "Parâmetro não simples declarado aqui.", "Not_all_code_paths_return_a_value_7030": "Nem todos os caminhos de código retornam um valor.", "Not_all_constituents_of_type_0_are_callable_2756": "Nem todos os membros do tipo '{0}' podem ser chamados.", "Not_all_constituents_of_type_0_are_constructable_2760": "Nem todos os membros do tipo '{0}' são construídos.", "Numbers_out_of_order_in_quantifier_1506": "Números fora de ordem no quantificador.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Os literais numéricos com valores absolutos iguais a 2^53 ou mais são muito grandes para serem representados precisamente como inteiros.", "Numeric_separators_are_not_allowed_here_6188": "Separadores numéricos não são permitidos aqui.", "Object_is_of_type_unknown_2571": "O objeto é do tipo 'desconhecido'.", "Object_is_possibly_null_2531": "Possivelmente, o objeto é 'nulo'.", "Object_is_possibly_null_or_undefined_2533": "Possivelmente, o objeto é 'nulo' ou 'indefinido'.", "Object_is_possibly_undefined_2532": "Possivelmente, o objeto é 'nulo'.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "O literal de objeto pode especificar apenas propriedades conhecidas e '{0}' não existe no tipo '{1}'.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "O literal de objeto pode especificar somente propriedades conhecidas, mas o '{0}' não existe no tipo '{1}'. Você queria ter escrito '{2}'?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "A propriedade '{0}' do literal de objeto implicitamente tem um tipo '{1}'.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "Objetos que contêm propriedades abreviadas não podem ser inferidos com --isolatedDeclarations.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "Os objetos que contêm atribuições de espalhamento não podem ser inferidos com --isolatedDeclarations.", "Octal_digit_expected_1178": "Dígito octal esperado.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "Sequências de escape octais e referências inversas não são permitidas em uma classe de caracteres. Se isso foi destinado a uma sequência de escape, use a sintaxe '{0}' em vez disso.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "Sequências de escape octais não são permitidas. Use a sintaxe '{0}'.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "Literais octais não são permitidos. Use a sintaxe '{0}'.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "Um valor de '{0}.{1}' é a cadeia de caracteres '{2}', e o outro é considerado um valor numérico desconhecido.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "É permitida apenas uma única declaração de variável em uma instrução 'for...in'.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "É permitida apenas uma única declaração de variável em uma instrução 'for...of'.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Apenas uma função void pode ser chamada com a palavra-chave 'new'.", "Only_ambient_modules_can_use_quoted_names_1035": "<PERSON>nte os módulos de ambiente podem usar nomes entre aspas.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Há suporte somente aos módulos 'amd' e 'system' ao lado de --{0}.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "Somente matrizes const podem ser inferidas com --isolatedDeclarations.", "Only_emit_d_ts_declaration_files_6014": "<PERSON>ita somente arquivos de declaração '.d.ts'.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Gerar somente arquivos .d.ts e não arquivos JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Somente métodos protegidos e públicos da classe base são acessíveis pela palavra-chave 'super'.", "Operator_0_cannot_be_applied_to_type_1_2736": "O operador '{0}' não pode ser aplicado ao tipo '{1}'.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "O operador '{0}' não pode ser aplicado aos tipos '{1}' e '{2}'.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "Os operadores não devem ser mistos em uma classe de caracteres. Encapsule-o em uma classe aninhada.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Recusar um projeto de verificação de referência de multiprojeto ao editar.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "A opção '{0}={1}' foi removida. Remova-a da configuração.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "A opção '{0}={1}' foi preterida e deixará de funcionar no TypeScript {2}. Especifique compilerOption '\"ignoreDeprecations\": \"{3}\"' para silenciar esse erro.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "A opção '{0}' somente pode ser especificada no arquivo 'tsconfig.json' ou definida como 'false' ou 'null' na linha de comando.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "A opção '{0}' somente pode ser especificada no arquivo 'tsconfig.json' ou definida como 'null' na linha de comando.", "Option_0_can_only_be_specified_on_command_line_6266": "A opção '{0}' só pode ser especificada na linha de comando.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "A opção '{0} só pode ser usada quando qualquer uma das opções '--inlineSourceMap' ou '--sourceMap' é fornecida.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "A opção '{0}' só pode ser usada quando 'moduleResolution' está definido como 'node16', 'nodenext' ou 'bundler'.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "A opção '{0}' só pode ser usada quando 'module' está definido como 'preserve' ou 'es2015' ou posterior.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "A opção '{0}' não pode ser especificada quando a opção 'jsx' é '{1}'.", "Option_0_cannot_be_specified_with_option_1_5053": "A opção '{0}' não pode ser especificada com a opção '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "A opção '{0}' não pode ser especificada sem especificar a opção '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "A opção '{0}' não pode ser especificada sem a especificação da opção '{1}' ou '{2}'.", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "A opção '{0}' foi removida. Remova-a da configuração.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "A opção '{0}' foi preterida e deixará de funcionar no TypeScript {1}. Especifique compilerOption '\"ignoreDeprecations\": \"{2}\"' para silenciar esse erro.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "A opção '{0}' é redundante e não pode ser especificada com a opção '{1}'.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "A opção 'allowImportingTsExtensions' só pode ser usada quando 'noEmit' ou 'emitDeclarationOnly' está definido.", "Option_build_must_be_the_first_command_line_argument_6369": "A opção '--build' precisa ser o primeiro argumento da linha de comando.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "A opção '--incremental' só pode ser especificada usando tsconfig, emitindo para um arquivo único ou quando a opção '--tsBuildInfoFile' for especificada.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "A opção 'isolatedModules' só pode ser usada quando nenhuma opção de '--module' for fornecida ou a opção 'target' for 'ES2015' ou superior.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "A opção 'moduleResolution' deve ser definida como '{0}' (ou deixada não especificada) quando a opção 'module' está definida como '{1}'.", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "A opção 'module' deve ser definida como '{0}' quando a opção 'moduleResolution' está definida como '{1}'.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "A opção 'preserveConstEnums' não pode ser desabilitada quando '{0}' está habilitada.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "A opção 'project' não pode ser mesclada com arquivos de origem em uma linha de comando.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "A opção '--resolveJsonModule' não pode ser especificada quando 'moduleResolution' está definido como 'classic'.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "A opção '--resolveJsonModule' não pode ser especificada quando 'module' está definido como 'none', 'system' ou 'umd'.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "A opção 'texttimModuleSyntax' não pode ser usada quando 'module' está definido como 'UMD', 'AMD' ou 'System'.", "Options_0_and_1_cannot_be_combined_6370": "As opções '{0}' e '{1}' não podem ser combinadas.", "Options_Colon_6027": "Opções:", "Output_Formatting_6256": "Formatação da Saída", "Output_compiler_performance_information_after_building_6615": "Gerar informações de desempenho do compilador após a criação.", "Output_directory_for_generated_declaration_files_6166": "Diretório de saída para os arquivos de declaração gerados.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "O arquivo de saída '{0}' não foi compilado do arquivo de origem '{1}'.", "Output_from_referenced_project_0_included_because_1_specified_1411": "Saída do projeto referenciado '{0}' incluída porque '{1}' está especificado", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Saída do projeto referenciado '{0}' incluída porque '--module' está especificado como 'none'", "Output_more_detailed_compiler_performance_information_after_building_6632": "Gerar informações de desempenho do compilador mais detalhadas após a criação.", "Overload_0_of_1_2_gave_the_following_error_2772": "A sobrecarga {0} de {1}, '{2}', gerou o seguinte erro.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Assinaturas de sobrecarga devem todas ser abstratas ou não abstratas.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "<PERSON><PERSON> as assinaturas de sobrecarga devem ser ambiente ou não ambiente.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Assinaturas de sobrecarga devem todas ser exportadas ou não exportadas.", "Overload_signatures_must_all_be_optional_or_required_2386": "<PERSON><PERSON> as assinaturas de sobrecarga devem ser opcionais ou obrigatórias.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "<PERSON><PERSON> as assinaturas de sobrecarga devem ser protegidas, privadas ou públicas.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "O parâmetro '{0}' não pode referenciar o identificador '{1}' declarado depois dele.", "Parameter_0_cannot_reference_itself_2372": "O parâmetro '{0}' não pode referenciar a si mesmo.", "Parameter_0_implicitly_has_an_1_type_7006": "O parâmetro '{0}' implicitamente tem um tipo '{1}'.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "O parâmetro '{0}' implicitamente tem um tipo '{1}', mas um tipo melhor pode ser inferido do uso.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "O parâmetro '{0}' não está na mesma posição que o parâmetro '{1}'.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "O parâmetro '{0}' do acessador tem ou está usando o nome '{1}' do módulo externo '{2}', mas não pode ser nomeado.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "O parâmetro '{0}' do acessador tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "O parâmetro '{0}' do acessador tem ou está usando o nome privado '{1}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "O parâmetro '{0}' da assinatura de chamada da interface exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "O parâmetro '{0}' da assinatura de chamada da interface exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "O parâmetro '{0}' do construtor da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "O parâmetro '{0}' do construtor da classe exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "O parâmetro '{0}' do construtor da classe exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "O parâmetro '{0}' da assinatura de construtor da interface exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "O parâmetro '{0}' da assinatura de construtor da interface exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "O parâmetro '{0}' da função exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "O parâmetro '{0}' da função exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "O parâmetro '{0}' da função exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "O parâmetro '{0}' da assinatura de índice da interface exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "O parâmetro '{0}' da assinatura de índice da interface exportadas tem ou está usando o nome privado '{1}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "O parâmetro '{0}' do método da interface exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "O parâmetro '{0}' do método da interface exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "O parâmetro '{0}' do método público da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "O parâmetro '{0}' do método público da classe exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "O parâmetro '{0}' do método público da classe exportada tem ou está usando o nome particular '{1}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "O parâmetro '{0}' do método estático público da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "O parâmetro '{0}' do método estático público da classe exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "O parâmetro '{0}' do método estático público da classe exportada tem ou está usando o nome particular '{1}'.", "Parameter_cannot_have_question_mark_and_initializer_1015": "O parâmetro não pode ter inicializador e ponto de interrogação.", "Parameter_declaration_expected_1138": "Declaração de parâmetro esperada.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "O parâmetro tem um nome, mas não um tipo. Você quis dizer '{0}: {1}'?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Modificadores de parâmetro só podem ser usados em arquivos TypeScript.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "O parâmetro deve ter uma anotação de tipo explícito com --isolatedDeclarations.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "O tipo de parâmetro do setter público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "O tipo de parâmetro do setter público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "O tipo de parâmetro do setter estático público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "O tipo de parâmetro do setter estático público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Analisar em modo estrito e emitir \"usar estrito\" para cada arquivo de origem.", "Part_of_files_list_in_tsconfig_json_1409": "Parte da lista 'files' no tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "O padrão '{0}' pode ter no máximo um caractere '*'.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Os tempos de desempenho de '--diagnostics' ou '--extendedDiagnostics' não estão disponíveis nesta sessão. Não foi possível encontrar uma implementação nativa da API de Desempenho Web.", "Platform_specific_6912": "Específico da plataforma", "Prefix_0_with_an_underscore_90025": "Prefixo '{0}' com um sublinhado", "Prefix_all_incorrect_property_declarations_with_declare_95095": "<PERSON>fixa<PERSON> to<PERSON> as declarações de propriedade incorretas com 'declare'", "Prefix_all_unused_declarations_with_where_possible_95025": "Prefixar com '_' to<PERSON> as declarações não usadas quando possível", "Prefix_with_declare_95094": "Prefixar com 'declare'", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Preserve os valores importados não usados na saída JavaScript que, de outra forma, seriam removidos.", "Print_all_of_the_files_read_during_the_compilation_6653": "Imprimir todos os arquivos lidos durante a compilação.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Arquivos de impressão lidos durante a compilação, incluindo o motivo de sua inclusão.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Imprima nomes de arquivos e o motivo pelo qual eles fazem parte da compilação.", "Print_names_of_files_part_of_the_compilation_6155": "Nomes de impressão das partes dos arquivos da compilação.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Imprima nomes de arquivos que fazem parte da compilação e, em seguida, interrompa o processamento.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Nomes de impressão das partes dos arquivos gerados da compilação.", "Print_the_compiler_s_version_6019": "Imprima a versão do compilador.", "Print_the_final_configuration_instead_of_building_1350": "Imprima a configuração final em vez de compilar.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Imprimir os nomes dos arquivos emitidos após uma compilação.", "Print_this_message_6017": "Imprima esta mensagem.", "Private_accessor_was_defined_without_a_getter_2806": "O acessador privado foi definido sem um getter.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "O campo particular '{0}' deve ser declarado em uma classe delimitadora.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Identificadores privados não são permitidos em declarações de variáveis.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Identificadores privados não são permitidos fora dos corpos de classe.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Identificadores privados são permitidos apenas em corpos de classe e só podem ser usados como parte de uma declaração de membro de classe, acesso de propriedade ou no lado esquerdo de uma expressão 'em'", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Identificadores privados só estão disponíveis ao direcionar para o ECMAScript 2015 ou superior.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Não é possível usar identificadores privados como parâmetros.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "O membro privado ou protegido '{0}' não pode ser acessado em um parâmetro de tipo.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "O projeto '{0}' está sendo reconstruído forçadamente", "Project_0_is_out_of_date_because_1_6420": "O projeto '{0}' está desatualizada porque {1}.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "O projeto '{0}' está desatualizada porque o arquivo buildinfo '{1}' indica que o arquivo '{2}' era o arquivo raiz da compilação, mas não é mais.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "O projeto '{0}' está desatualizado porque o arquivo buildinfo '{1}' indica que o programa precisa relatar erros.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "O projeto '{0}' está desatualizado porque o arquivo buildinfo '{1}' indica que algumas das alterações não foram emitidas", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "O projeto '{0}' está desatualizado porque o arquivo buildinfo '{1}' indica que há alteração em compilerOptions", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "O projeto '{0}' está desatualizado porque sua dependência '{1}' está desatualizada", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "O projeto '{0}' está desatualizado porque a saída '{1}' é mais antiga que a entrada '{2}'", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "O projeto '{0}' está desatualizado porque o arquivo de saída '{1}' não existe", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "O projeto '{0}' está desatualizado porque a saída foi gerada com a versão '{1}' que difere da versão atual '{2}'", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "O projeto '{0}' está desatualizado porque ocorreu um erro ao ler o arquivo '{1}'", "Project_0_is_up_to_date_6361": "O projeto '{0}' está atualizado", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "O projeto '{0}' está atualizado porque a entrada mais recente '{1}' é mais antiga que a saída '{2}'", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "O projeto '{0}' está atualizado, mas precisa atualizar os registros de data e hora dos arquivos de saída mais antigos que os arquivos de entrada", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "O projeto '{0}' está atualizado com os arquivos .d.ts de suas dependências", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Referências de projeto não podem formar um gráfico circular. Ciclo detectado: {0}", "Projects_6255": "Projetos", "Projects_in_this_build_Colon_0_6355": "Projetos neste build: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Propriedades com o modificador 'acessador' estão disponíveis somente quando o alvo é ECMAScript 2015 e superior.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "A propriedade '{0}' não pode ter um inicializador, pois está marcado como abstrato.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "A propriedade '{0}' vem de uma assinatura de índice, portanto, ela precisa ser acessada com ['{0}'].", "Property_0_does_not_exist_on_type_1_2339": "A propriedade '{0}' não existe no tipo '{1}'.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "A propriedade '{0}' não existe no tipo '{1}'. Você quis dizer '{2}'?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "A propriedade '{0}' não existe no tipo '{1}'. Você queria acessar o membro estático '{2}'?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "A propriedade '{0}' não existe no tipo '{1}'. Você precisa alterar sua biblioteca de destino? Tente alterar a opção 'lib' do compilador para '{2}' ou posterior.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "A propriedade '{0}' não existe no tipo '{1}'. Tente alterar a opção de compilador 'lib' para incluir 'dom'.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "A propriedade “{0}” não tem nenhum inicializador e não está definitivamente atribuída no bloco estático de classe.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "A propriedade '{0}' não tem nenhum inicializador e não está definitivamente atribuída no construtor.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "A propriedade '{0}' tem implicitamente o tipo 'any' porque o acessador get não tem uma anotação de tipo de retorno.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "A propriedade '{0}' tem implicitamente o tipo 'any' porque o acessador set não tem uma anotação de tipo de parâmetro.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "A propriedade '{0}' implicitamente tem o tipo 'any', mas um tipo melhor para o acessador get pode ser inferido do uso.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "A propriedade '{0}' implicitamente tem o tipo 'any', mas um tipo melhor para o acessador set pode ser inferido do uso.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "A propriedade '{0}' no tipo '{1}' não pode ser atribuída à mesma propriedade no tipo base '{2}'.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "A propriedade '{0}' no tipo '{1}' não pode ser atribuída ao tipo '{2}'.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "A propriedade '{0}' no tipo '{1}' se refere a um membro diferente que não pode ser acessado por meio do tipo '{2}'.", "Property_0_is_declared_but_its_value_is_never_read_6138": "A propriedade '{0}' é declarada, mas seu valor nunca é lido.", "Property_0_is_incompatible_with_index_signature_2530": "A propriedade '{0}' é incompatível com a assinatura de índice.", "Property_0_is_missing_in_type_1_2324": "A propriedade '{0}' está ausente no tipo '{1}'.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "A propriedade '{0}' está ausente no tipo '{1}', mas é obrigatória no tipo '{2}'.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "A propriedade '{0}' não é acessível fora da classe '{1}' porque tem um identificador privado.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "A propriedade '{0}' é opcional no tipo '{1}', mas obrigatória no tipo '{2}'.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "A propriedade '{0}' é particular e somente é acessível na classe '{1}'.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "A propriedade '{0}' é particular no tipo '{1}', mas não no tipo '{2}'.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "A propriedade '{0}' está protegida e só pode ser acessada por meio de uma instância da classe '{1}'. Esta é uma instância da classe '{2}'.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "A propriedade '{0}' é protegida e somente é acessível na classe '{1}' e em suas subclasses.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "A propriedade '{0}' é protegida, mas o tipo '{1}' não é uma classe derivada de '{2}'.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "A propriedade '{0}' é protegida no tipo '{1}', mas pública no tipo '{2}'.", "Property_0_is_used_before_being_assigned_2565": "A propriedade '{0}' é usada antes de ser atribuída.", "Property_0_is_used_before_its_initialization_2729": "A propriedade '{0}' é usada antes da inicialização.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "A propriedade pode não existir '{0}' no tipo '{1}'. Voc<PERSON> quis dizer '{2}'?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "A propriedade \"{0}\" do atributo de espalhamento JSX não pode ser atribuída à propriedade de destino.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "A propriedade '{0}' do tipo de classe anônima exportada pode não ser privada ou protegida.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "A propriedade '{0}' da interface exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "A propriedade '{0}' da interface exportada tem ou está usando o nome particular '{1}'.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "A propriedade '{0}' do tipo '{1}' não pode ser atribuída ao '{2}' tipo de índice '{3}'.", "Property_0_was_also_declared_here_2733": "A propriedade '{0}' também foi declarada aqui.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "A propriedade '{0}' substituirá a propriedade base em '{1}'. Se isso for intencional, adicione um inicializador. <PERSON><PERSON>o contrá<PERSON>, adicione um modificador 'declare' ou remova a declaração redundante.", "Property_assignment_expected_1136": "Atribuição de propriedade esperada.", "Property_destructuring_pattern_expected_1180": "Padrão de desestruturação de propriedade esperado.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "A propriedade deve ter uma anotação de tipo explícita com --isolatedDeclarations.", "Property_or_signature_expected_1131": "Propriedade ou assinatura esperada.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "O valor da propriedade pode ser somente um literal de cadeia, um literal numérico, 'true', 'false', 'null', literal de objeto ou literal de matriz.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "Fornecer suporte completo para os iteráveis em 'for-of', espalhamento e desestruturação ao direcionar 'ES5'.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "O método público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "O método público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "O método público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "A propriedade pública '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeada.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "A propriedade pública '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "A propriedade pública '{0}' da classe exportada tem ou está usando o nome particular '{1}'.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "O método estático público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "O método estático público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "O método estático público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "A propriedade estática pública '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeada.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "A propriedade estática pública '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo particular '{2}'.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "A propriedade estática pública '{0}' da classe exportada tem ou está usando o nome particular '{1}'.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "O nome qualificado '{0}' não é permitido sem um '@param {object} {1}' à esquerda.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "Gerar um erro quando um parâmetro de função não for lido.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Gerar erro em expressões e declarações com um tipo 'any' implícito.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Gerar erro em expressões 'this' com um tipo 'any' implícito.", "Range_out_of_order_in_character_class_1517": "Intervalo fora de ordem na classe de caracteres.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "Exportar novamente um tipo quando '{0}' está habilitado requer o uso de 'tipo de exportação'.", "React_components_cannot_include_JSX_namespace_names_2639": "Os componentes react não podem incluir nomes de namespace JSX", "Redirect_output_structure_to_the_directory_6006": "Redirecione a estrutura de saída para o diretório.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Reduzir o número de projetos carregados automaticamente pelo TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "O projeto referenciado '{0}' pode não desabilitar a emissão.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "O projeto referenciado '{0}' deve ter a configuração de \"composite\": true.", "Referenced_via_0_from_file_1_1400": "Refer<PERSON>ciado via '{0}' do arquivo '{1}'", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "Os caminhos de importação relativos precisam de extensões de arquivo explícitas nas importações do ECMAScript quando '--moduleResolution' for 'node16' ou 'nodenext'. Considere adicionar uma extensão ao caminho de importação.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "Os caminhos de importação relativos precisam de extensões de arquivo explícitas nas importações do ECMAScript quando '--moduleResolution' for 'node16' ou 'nodenext'. Você quis dizer '{0}'?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Remova uma lista de diretórios do processo de inspeção.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Remover uma lista de arquivos do processamento do modo de inspeção.", "Remove_all_unnecessary_override_modifiers_95163": "Remover todos os modificadores 'override' desnecessários", "Remove_all_unnecessary_uses_of_await_95087": "Remover todos os usos desnecessários de 'await'", "Remove_all_unreachable_code_95051": "Remover todo o código inacessível", "Remove_all_unused_labels_95054": "Remover todos os rótulos não utilizados", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Remover as chaves de todos os corpos de função de seta com problemas relevantes", "Remove_braces_from_arrow_function_95060": "Remover chaves da função de seta", "Remove_braces_from_arrow_function_body_95112": "Remover as chaves do corpo de função de seta", "Remove_import_from_0_90005": "Remover importação de '{0}'", "Remove_override_modifier_95161": "Remover o modificador 'override'", "Remove_parentheses_95126": "Remover os parênteses", "Remove_template_tag_90011": "Remover marca de modelo", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Remover o limite de 20MB no tamanho total do código-fonte para arquivos JavaScript no servidor de linguagem TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Remover 'type' da declaração de importação \"{0}\"", "Remove_type_from_import_of_0_from_1_90056": "Remover 'type' da importação de '{0}' de \"{1}\"", "Remove_type_parameters_90012": "Remover parâmetros de tipo", "Remove_unnecessary_await_95086": "Remover 'await' des<PERSON><PERSON><PERSON><PERSON>", "Remove_unreachable_code_95050": "Remover código inacessível", "Remove_unused_declaration_for_Colon_0_90004": "Remover declaração não usada para: '{0}'", "Remove_unused_declarations_for_Colon_0_90041": "<PERSON><PERSON><PERSON> as declarações não usadas de: '{0}'", "Remove_unused_destructuring_declaration_90039": "Remova a declaração de desestruturação não usada", "Remove_unused_label_95053": "Remover rótulo não utilizado", "Remove_variable_statement_90010": "Remover instrução de variável", "Rename_param_tag_name_0_to_1_95173": "Renomear o nome da marca '@param' '{0}' como '{1}'", "Replace_0_with_Promise_1_90036": "Substituir '{0}' por 'Promise<{1}>'", "Replace_all_unused_infer_with_unknown_90031": "Substituir todos os 'infer' não usados por 'unknown'", "Replace_import_with_0_95015": "Substitua a importação com '{0}'.", "Replace_infer_0_with_unknown_90030": "Substituir 'infer {0}' por 'unknown'", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Relate erro quando nem todos os caminhos de código na função retornarem um valor.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Relate erros para casos de fallthrough na instrução switch.", "Report_errors_in_js_files_8019": "Relatar erros em arquivos .js.", "Report_errors_on_unused_locals_6134": "Relatar erros nos locais não utilizados.", "Report_errors_on_unused_parameters_6135": "Relatar erros nos parâmetros não utilizados.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "Exigir anotação suficiente em exportações para que outras ferramentas possam gerar arquivos de declaração trivialmente.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "<PERSON><PERSON><PERSON> que as propriedades não declaradas de assinaturas de índice usem acessos de elemento.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Os parâmetros de tipo necessários podem não seguir os parâmetros de tipo opcionais.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "A resolução para o módulo '{0}' foi encontrada no cache do local '{1}'.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "A resolução para a diretiva de referência de tipo '{0}' foi encontrada no cache a partir do local '{1}'.", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "Falha na resolução de nome não relativo; tentando com recursos modernos de resolução de Nó desabilitados para ver se a biblioteca npm precisa de atualização de configuração.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "Falha na resolução de nome não relativo; tentando com '--moduleResolution bundler' para ver se o projeto pode precisar de atualização de configuração.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "Resolva 'keyof' somente para nomes de propriedades com valores de cadeia de caracteres (sem números nem símbolos).", "Resolved_under_condition_0_6414": "<PERSON>solvid<PERSON> sob a condição '{0}'.", "Resolving_in_0_mode_with_conditions_1_6402": "Resolvendo no modo {0} com condições {1}.", "Resolving_module_0_from_1_6086": "======== Resolvendo módulo '{0}' de '{1}'. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Resolvendo nome de módulo '{0}' relativo à URL base '{1}' - '{2}'.", "Resolving_real_path_for_0_result_1_6130": "Resolvendo o caminho real de '{0}', resultado '{1}'.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Resolvendo a diretiva de tipo de referência '{0}', contendo o arquivo '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Resolvendo diretiva de referência de tipo '{0}', arquivo contido '{1}', diretório raiz '{2}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Resolvendo diretiva de referência de tipo '{0}', arquivo contido '{1}', diretório raiz não configurado. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Resolvendo diretiva de referência de tipo '{0}', arquivo contido não configurado, diretório raiz '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Resolvendo diretiva de referência de tipo '{0}', arquivo contido não configurado, diretório raiz não configurado. ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "Resolvendo diretiva de referência de tipo para o programa que especifica typeRoots personalizado, ignorando a pesquisa na pasta 'node_modules'.", "Resolving_with_primary_search_path_0_6121": "Resolvendo com caminho de pesquisa primário '{0}'.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "O parâmetro rest '{0}' implicitamente tem um tipo 'any[]'.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "O parâmetro REST '{0}' implicitamente tem um tipo 'any[]', mas um tipo melhor pode ser inferido do uso.", "Rest_types_may_only_be_created_from_object_types_2700": "Os tipos de rest podem ser criado somente de tipos de objeto.", "Return_type_annotation_circularly_references_itself_2577": "A anotação de tipo de retorno faz referência circular a si mesma.", "Return_type_must_be_inferred_from_a_function_95149": "O tipo de retorno precisa ser inferido de uma função", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "O tipo de retorno da assinatura de chamada da interface exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "O tipo de retorno da assinatura de chamada da interface exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "O tipo de retorno da assinatura de construtor da interface exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "O tipo de retorno da assinatura de construtor da interface exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "O tipo de retorno da assinatura de construtor deve ser atribuível ao tipo de instância da classe.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "O tipo de retorno da função exportada tem ou está usando o nome '{0}' do módulo externo {1}, mas não pode ser nomeado.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "O tipo de retorno da função exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "O tipo de retorno da função exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "O tipo de retorno da assinatura de índice da interface exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "O tipo de retorno da assinatura de índice da interface exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "O tipo de retorno do método da interface exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "O tipo de retorno do método da interface exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "O tipo de retorno do getter público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo {2}, mas não pode ser nomeado.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "O tipo de retorno do getter público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "O tipo de retorno do getter público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "O tipo de retorno do método público da classe exportada tem ou está usando o nome '{0}' do módulo externo {1}, mas não pode ser nomeado.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "O tipo de retorno do método público da classe exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "O tipo de retorno do método público da classe exportada tem ou está usando o nome particular '{0}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "O tipo de retorno do getter estático público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo externo '{2}', mas não pode ser nomeado.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "O tipo de retorno do getter estático público '{0}' da classe exportada tem ou está usando o nome '{1}' do módulo privado '{2}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "O tipo de retorno do getter estático público '{0}' da classe exportada tem ou está usando o nome privado '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "O tipo de retorno do método estático público da classe exportada tem ou está usando o nome '{0}' do módulo externo {1}, mas não pode ser nomeado.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "O tipo de retorno do método estático público da classe exportada tem ou está usando o nome '{0}' do módulo particular '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "O tipo de retorno do método estático público da classe exportada tem ou está usando o nome particular '{0}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Reutilizando a resolução do módulo '{0}' de '{1}' encontrado no cache a partir da localização '{2}', não foi resolvido.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Reutilizando a resolução do módulo '{0}' de '{1}' encontrado no cache de localização '{2}', foi resolvido com sucesso para '{3}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Reutilizando a resolução do módulo '{0}' de '{1}' encontrado no cache a partir da localização '{2}', foi resolvido com sucesso para '{3}' com ID do Pacote '{4}'.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Reutilizando a resolução do módulo '{0}' de '{1}' do antigo programa, não foi resolvido.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Reutilizando a resolução do módulo '{0}' de '{1}' do antigo programa, foi resolvido com sucesso para '{2}'.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Reutilizando a resolução do módulo '{0}' de '{1}' do antigo programa, foi resolvido com sucesso para '{2}' com a ID do pacote '{3}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Reutilizando a resolução do tipo diretiva de referência '{0}' de '{1}' encontrado no cache de localização '{2}', não foi resolvido.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Reutilizando a resolução do tipo diretiva de referência '{0}' de '{1}' encontrado no cache de localização '{2}', foi resolvido com sucesso para '{3}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Reutilizando a resolução do tipo diretiva de referência '{0}' de '{1}' encontrado no cache de localização '{2}', foi resolvido com sucesso '{3}' com ID do Pacote '{4}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Reutilizando a resolução do tipo diretiva de referência '{0}' de '{1}' do antigo programa, não foi resolvido.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Reutilizando a resolução do tipo diretiva de referência '{0}' de '{1}' do antigo programa, foi resolvido com sucesso para '{2}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Reutilizando a resolução do tipo diretriz de referência '{0}' de '{1}' do antigo programa, foi resolvido com sucesso para '{2}' com ID do Pacote '{3}'.", "Rewrite_all_as_indexed_access_types_95034": "Reescrever tudo como tipos de acesso indexados", "Rewrite_as_the_indexed_access_type_0_90026": "Reescrever como o tipo de acesso indexado '{0}'", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "Reescreva as extensões de arquivo \".ts\", \".tsx\", \".mts\" e \".cts\" em caminhos de importação relativos para seu equivalente em JavaScript nos arquivos de saída.", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "Operando direito de ?? está inacessível porque o operando esquerdo nunca é nulo.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Diretório raiz não pode ser determinado, ignorando caminhos de pesquisa primários.", "Root_file_specified_for_compilation_1427": "Arquivo raiz especificado para compilação", "STRATEGY_6039": "ESTRATÉGIA", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Salvar arquivos .tsbuildinfo para permitir a compilação incremental de projetos.", "Saw_non_matching_condition_0_6405": "Viu condição de não correspondência '{0}'.", "Scoped_package_detected_looking_in_0_6182": "Pacote com escopo detectado, procurando no '{0}'", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "Pesquisando todos os diretórios node_modules ancestrais em busca de extensões de fallback: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "Pesquisando todos os diretórios node_modules ancestrais em busca de extensões preferenciais: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "A seleção não é uma instrução ou instruções válidas", "Selection_is_not_a_valid_type_node_95133": "A seleção não é um nó de tipo válido", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Definir a versão do idioma do JavaScript para o JavaScript emitido e incluir as declarações de biblioteca compatíveis.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Definir o idioma das mensagens do TypeScript. Isso não afeta a emissão.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Defina a opção 'module' no arquivo de configuração para '{0}'", "Set_the_newline_character_for_emitting_files_6659": "Definir o caractere de nova linha para a emissão de arquivos.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Defina a opção 'target' no arquivo de configuração para '{0}'", "Setters_cannot_return_a_value_2408": "Setters não podem retornar um valor.", "Show_all_compiler_options_6169": "<PERSON><PERSON> todas as opções do compilador.", "Show_diagnostic_information_6149": "Mostras as informações de diagnóstico.", "Show_verbose_diagnostic_information_6150": "Mostras as informações detalhadas de diagnóstico.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "Mostrar o que seria compilado (ou excluído, se especificado com '--clean')", "Signature_0_must_be_a_type_predicate_1224": "A assinatura '{0}' deve ser um predicado de tipo.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "As declarações de assinatura só podem ser usadas em arquivos TypeScript.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "Ignorar a criação de projetos downstream em caso de erro no projeto upstream.", "Skip_type_checking_all_d_ts_files_6693": "Ignorar verificação de tipo de todos os arquivos .d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Ignorar verificação de tipo de arquivos .d.ts que estão incluídos com TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Ignorar a verificação de tipo dos arquivos de declaração.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "Ignorando o módulo '{0}' que se parece com um URI absoluto, tipos de arquivo de destino: {1}.", "Source_from_referenced_project_0_included_because_1_specified_1414": "Origem do projeto referenciado '{0}' incluída porque '{1}' está especificado", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "Origem do projeto referenciado '{0}' incluída porque '--module' está especificado como 'none'", "Source_has_0_element_s_but_target_allows_only_1_2619": "A origem tem {0} elementos, mas o destino permite somente {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "A origem tem {0} elementos, mas o destino exige {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "A fonte não fornece nenhuma correspondência para o elemento necessário na posição {0} no destino.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "A fonte não fornece nenhuma correspondência para o elemento variádico na posição {0} no destino.", "Specify_ECMAScript_target_version_6015": "Especifique a versão de destino do ECMAScript.", "Specify_JSX_code_generation_6080": "Especifique a geração do código JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Especificar um arquivo que agrupa todas as saídas em um arquivo JavaScript. Se 'declaração' for true, também designará um arquivo que incluirá todas as saídas .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Especificar uma lista de padrões glob que correspondam aos arquivos a serem incluídos na compilação.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Especificar uma lista de plug-ins de serviço de linguagem a incluir.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Especificar um conjunto de arquivos de declaração de biblioteca empacotados que descreva o ambiente de tempo de runtime de destino.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Especificar um conjunto de entradas que remapeiem importações para locais de pesquisa adicionais.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Especifique uma matriz de objetos que especificam caminhos para projetos. Usado em referências de projeto.", "Specify_an_output_folder_for_all_emitted_files_6678": "Especificar uma pasta de saída para todos os arquivos emitidos.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Especificar o comportamento de emissão/verificação para importações que são usadas somente para tipos.", "Specify_file_to_store_incremental_compilation_information_6380": "Especificar arquivo para armazenar informações de compilação incremental", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Especifique como o TypeScript procura um arquivo de um determinado especificador de módulo.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Especificar como os diretórios são observados nos sistemas que não têm a funcionalidade recursiva de inspeção de arquivo.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Especifique como funciona o modo de inspeção TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Especifique os arquivos de biblioteca a serem incluídos na compilação.", "Specify_module_code_generation_6016": "Especifique a geração do código do módulo.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Especificar o especificador de módulo usado para importar as funções de fábrica JSX ao usar 'jsx: react-jsx*'.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Especificar várias pastas que agem como './node_modules/@types '.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Especificar uma ou mais referências de módulo ou nó para os arquivos de configuração base dos quais as configurações são herdadas.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Especifique opções para aquisição automática de arquivos de declaração.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Especifique a estratégia para criar uma inspeção de sondagem quando não conseguir criar usando eventos do sistema de arquivos: 'FixedInterval' (padrão), 'PriorityInterval', 'DynamicPriority', 'FixedChunkSize'.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Especifique a estratégia para observar ao diretório em plataformas que não têm suporte à observação recursiva nativamente: 'UseFsEvents' (padrão), 'FixedPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling'.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Especifique a estratégia para observar ao arquivo: 'FixedPollingInterval' (padrão), 'PriorityPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling', 'UseFsEvents', 'UseFsEventsOnParentDirectory'.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Especifique a referência do fragmento JSX usada para fragmentos ao direcionar o React JSX emit, por exemplo, 'React.Fragment' ou 'Fragment'.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Especifique a função de fábrica JSX a ser usada ao direcionar a emissão 'react' do JSX, por ex., 'React.createElement' ou 'h'.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Especifique a função de fábrica JSX usada ao direcionar o React JSX emit, por exemplo, 'React.createElement' ou 'h'.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Especifique a função de alocador do fragmento JSX a ser usada no direcionamento de uma emissão de JSX 'react' com a opção do compilador 'jsxFactory' especificada, por exemplo, 'Fragment'.", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Especificar o diretório base para resolver nomes de módulos não relativos.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Especifique o fim da sequência de linha a ser usado ao emitir arquivos: 'CRLF' (dos) ou 'LF' (unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Especifique o local onde o depurador deve localizar arquivos TypeScript em vez de locais de origem.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Especifique o local onde o depurador deve localizar arquivos de mapa em vez de locais gerados.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Especifique a profundidade máxima da pasta usada para verificar os arquivos JavaScript de 'node_modules'. Aplicável apenas com 'allowJs'.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Especifique o especificador do módulo a ser utilizado para importar as funções 'jsx' e 'jsxs' de fábrica, por exemplo, react", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Especifique o objeto invocado para 'createElement'. Isso se aplica apenas ao direcionar a emissão JSX 'react'.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Especifique o diretório de saída para os arquivos de declaração gerados.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Especifique o caminho para o arquivo de compilação incremental .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Especifique o diretório raiz de arquivos de entrada. Use para controlar a estrutura do diretório de saída com --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Especifique a pasta raiz em seus arquivos de origem.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Especifique o caminho raiz para que os depuradores localizem o código-fonte de referência.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Especifique os nomes dos pacotes de tipo a serem incluídos sem serem referenciados em um arquivo de origem.", "Specify_what_JSX_code_is_generated_6646": "Especifique qual código JSX é gerado.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Especificar qual abordagem a inspeção deverá usar se o sistema ficar sem os observadores de arquivo nativos.", "Specify_what_module_code_is_generated_6657": "Especifique qual código do módulo é gerado.", "Split_all_invalid_type_only_imports_1367": "<PERSON><PERSON><PERSON> to<PERSON> as importações somente de tipo inválidas", "Split_into_two_separate_import_declarations_1366": "Dividir em duas declarações de importação separadas", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "O operador de espalhamento só está disponível em expressões 'new' no direcionamento a ECMAScript 5 e superior.", "Spread_types_may_only_be_created_from_object_types_2698": "Os tipos de espalhamento podem ser criados apenas de tipos de objeto.", "Starting_compilation_in_watch_mode_6031": "Iniciando compilação no modo de inspeção...", "Statement_expected_1129": "Instrução esperada.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Instruções não são permitidas em contextos de ambiente.", "Static_members_cannot_reference_class_type_parameters_2302": "Membros estáticos não podem fazer referência a parâmetros de tipo de classe.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "Conflitos de propriedade estática '{0}' com propriedade interna 'Function.{0}' da função de construtor '{1}'.", "String_literal_expected_1141": "Literal de cadeia de caracteres esperado.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "Não há suporte para nomes de importação e exportação de literal de cadeia de caracteres quando o sinalizador '--module' está definido como 'es2015' ou 'es2020'.", "String_literal_with_double_quotes_expected_1327": "Literal de cadeia com aspas duplas é esperado.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Est<PERSON>zar erros e mensagens usando cor e contexto (experimental).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "Sinalizadores de subpadrão devem estar presentes quando há um sinal de subtração.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Declarações de propriedade subsequentes devem ter o mesmo tipo. A propriedade '{0}' deve ser do tipo '{1}', mas aqui tem o tipo '{2}'.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Declarações de variável subsequentes devem ter o mesmo tipo. A variável '{0}' deve ser do tipo '{1}', mas aqui tem o tipo '{2}'.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "A substituição '{0}' para o padrão '{1}' tem um tipo incorreto, 'string' esperada, obteve '{2}'.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "A substituição '{0}' no padrão '{1}' pode ter no máximo um caractere '*'.", "Substitutions_for_pattern_0_should_be_an_array_5063": "As substituições para o padrão '{0}' devem ser uma matriz.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Substituições para o padrão '{0}' não devem ser uma matriz vazia.", "Successfully_created_a_tsconfig_json_file_6071": "Arquivo tsconfig.json criado com êxito.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "As chamadas super não são permitidas fora dos construtores ou em funções aninhadas dentro dos construtores.", "Suppress_excess_property_checks_for_object_literals_6072": "Verificações de propriedade de excesso de compactação para literais de objeto.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Suprimir erros de noImplicitAny para objetos de indexação sem assinaturas de índice.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Sup<PERSON>ir erros 'noImplicitAny' ao indexar objetos que não têm assinaturas de índice.", "Switch_each_misused_0_to_1_95138": "Mude cada '{0}' usado incorretamente para '{1}'", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Chame sincronicamente retornos de chamadas e atualize o estado de observadores de diretório em plataformas que não têm suporte à observação recursiva nativamente.", "Syntax_Colon_0_6023": "Sintaxe: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "A tag '{0}' espera no mínimo '{1}' argumentos, mas o alocador JSX '{2}' fornece no máximo '{3}'.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Expressões de modelo marcado não são permitidas em uma cadeia opcional.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "O destino permite apenas {0} elementos, mas a origem pode ter mais.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "O destino exige {0} elementos, mas a origem pode ter menos.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "A assinatura de destino fornece poucos argumentos. Esperava {0} ou mais, mas obteve {1}.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "O modificador '{0}' só pode ser usado em arquivos TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "O operador '{0}' não pode ser aplicado ao tipo 'symbol'.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "O operador '{0}' não é permitido para tipos boolianos. Considere usar '{1}'.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "A propriedade '{0}' de um iterador assíncrono deve ser um método.", "The_0_property_of_an_iterator_must_be_a_method_2767": "A propriedade '{0}' de um iterador deve ser um método.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "O tipo 'Objeto' pode ser atribuído para muito poucos outros tipos. Você desejava usar o tipo 'qualquer' ao invés disso?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "O sinalizador Unicode (u) e o sinalizador Conjuntos Unicode (v) não podem ser definidos simultaneamente.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "O objeto 'arguments' não pode ser referenciado em uma função de seta em ES5. Considere usar uma expressão de função padrão.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "O objeto 'arguments' não pode ser referenciado em uma função assíncrona ou o método no ES5. Considere usar uma função ou um método padrão.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "O corpo de uma instrução 'if' não pode ser uma instrução vazia.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "A chamada teria sido bem-sucedida nesta implementação, mas as assinaturas de implementação de sobrecargas não estão visíveis externamente.", "The_character_set_of_the_input_files_6163": "O conjunto de caracteres dos arquivos de entrada.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "A função de seta contida captura o valor global de 'this'.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "O corpo da função ou do módulo contido é muito grande para a análise de fluxo de controle.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "O arquivo atual é um módulo CommonJS e não pode usar 'await' no nível superior.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "O arquivo atual é um módulo CommonJS cujas importações produzirão chamadas 'require'; no entanto, o arquivo referenciado é um módulo ECMAScript e não pode ser importado com 'require'. Considere escrever uma chamada 'import(\"{0}\")' dinâmica em vez disso.", "The_current_host_does_not_support_the_0_option_5001": "O host atual não dá suporte à opção '{0}'.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "A declaração de '{0}' que você provavelmente pretende usar é definida aqui", "The_declaration_was_marked_as_deprecated_here_2798": "A declaração foi marcada como preterida aqui.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "O tipo esperado vem da propriedade '{0}', que é declarada aqui no tipo '{1}'", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "O tipo esperado vem do tipo de retorno dessa assinatura.", "The_expected_type_comes_from_this_index_signature_6501": "O tipo esperado vem dessa assinatura de índice.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "A expressão de uma atribuição de exportação deve ser um identificador ou nome qualificado em um contexto de ambiente.", "The_file_is_in_the_program_because_Colon_1430": "O arquivo está no programa porque:", "The_files_list_in_config_file_0_is_empty_18002": "A lista de 'arquivos' no arquivo de configuração '{0}' está vazia.", "The_first_export_default_is_here_2752": "O primeiro padrão de exportação está aqui.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "O primeiro parâmetro do método 'then' de uma promessa deve ser um retorno de chamada.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "O tipo global 'JSX.{0}' não pode ter mais de uma propriedade.", "The_implementation_signature_is_declared_here_2750": "A assinatura de implementação é declarada aqui.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "A meta da propriedade 'import.meta' não é permitida em arquivos que serão compilados na saída do CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "A meta-propriedade 'import.meta' só é permitida quando a opção '--module' é 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18' ou 'nodenext'.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "O tipo inferido de '{0}' não pode ser nomeado sem uma referência a '{1}'. Isso provavelmente não é portátil. Uma anotação de tipo é necessária.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "O tipo inferido '{0}' faz referência a um tipo com uma estrutura cíclica que não pode ser serializada trivialmente. Uma anotação de tipo é necessária.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "O tipo inferido de '{0}' faz referência a um tipo '{1}' inacessível. Uma anotação de tipo é necessária.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "O tipo inferido deste nó excede o tamanho máximo que o compilador serializará. Uma anotação de tipo explícita é necessária.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "O inicializador de uma declaração 'using' deve ser um objeto com um método '[Symbol.dispose]()' ou ser 'null' ou 'undefined'.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "O inicializador de uma declaração 'await using' deve ser um objeto com um método '[Symbol.asyncDispose]()' ou '[Symbol.dispose]5D;()' ou ser 'null' ou 'undefined'.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "A interseção '{0}' foi reduzida para 'never' porque a propriedade '{1}' existe em vários constituintes e é privada em alguns.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "A interseção '{0}' foi reduzida para 'never' porque a propriedade '{1}' tem tipos conflitantes em alguns constituintes.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "A palavra-chave 'intrinsic' só pode ser usada para declarar tipos intrínsecos fornecidos pelo compilador.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "A opção do compilador 'jsxFragmentFactory' precisa ser fornecida para que se possa usar fragmentos JSX com a opção do compilador 'jsxFactory'.", "The_last_overload_gave_the_following_error_2770": "A última sobrecarga gerou o seguinte erro.", "The_last_overload_is_declared_here_2771": "A última sobrecarga é declarada aqui.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "O lado esquerdo de uma instrução 'for...in' não pode ser um padrão de desestruturação.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "O lado esquerdo de uma instrução 'for...in' não pode ser uma declaração 'using'.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "O lado esquerdo de uma instrução 'for...in' não pode ser uma declaração 'await using'.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "O lado esquerdo de uma instrução 'for...in' não pode usar uma anotação de tipo.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "O lado esquerdo de uma instrução 'for...in' pode não ser um acesso opcional de propriedade.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "O lado esquerdo de uma instrução 'for...in' deve ser uma variável ou um acesso à propriedade.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "O lado esquerdo de uma instrução de 'for...in' deve ser do tipo 'string' ou 'any'.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "O lado esquerdo de uma instrução 'for...of' não pode usar uma anotação de tipo.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "O lado esquerdo de uma instrução 'for...of' pode não ser um acesso opcional de propriedade.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "O lado esquerdo de uma instrução 'for...of' não pode ser 'assíncrono'.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "O lado esquerdo de uma instrução 'for...of' deve ser uma variável ou um acesso à propriedade.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "O lado esquerdo de uma operação aritmética deve ser do tipo 'any', 'number', 'bigint' ou um tipo enumerado.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "O lado esquerdo de uma expressão de atribuição pode não ser um acesso opcional de propriedade.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "O lado esquerdo de uma expressão de atribuição deve ser uma variável ou um acesso à propriedade.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "O lado esquerdo de uma expressão 'instanceof' deve ser atribuível ao primeiro argumento do método '[Symbol.hasInstance]' do lado direito.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "O lado esquerdo de uma expressão 'instanceof' deve ser do tipo 'any', um tipo de objeto ou um parâmetro de tipo.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "O local usado ao exibir mensagens ao usuário (por exemplo, 'en-us')", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "A profundidade máxima de dependência a ser pesquisada em arquivos node_modules e de carregamento de JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "O operando de um operador 'delete' pode não ser um identificador privado.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "O operando de um operador 'delete' não pode ser uma propriedade somente leitura.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "O operando de um operador 'delete' deve ser uma referência de propriedade.", "The_operand_of_a_delete_operator_must_be_optional_2790": "O operando de um operador 'delete' precisa ser opcional.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "O operando de um operador de incremento ou decremento pode não ser um acesso opcional de propriedade.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "O operando de um operador de incremento ou decremento deve ser uma variável ou um acesso à propriedade.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "O analisador esperava localizar um '{1}' para corresponder ao token '{0}' aqui.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "A raiz do projeto é ambígua, mas é necessária para resolver a entrada de mapa de exportação '{0}' no arquivo '{1}'. Forneça a opção do compilador `rootDir` para desambiguar.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "A raiz do projeto é ambígua, mas é necessária para resolver a entrada do mapa de importação '{0}' no arquivo '{1}'. Forneça a opção do compilador `rootDir` para desambiguar.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "A propriedade '{0}' não pode ser acessada no tipo '{1}' dentro dessa classe porque ela é sombreada por outro identificador privado com a mesma grafia.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "O tipo de retorno de uma função de decorador de parâmetro deve ser 'void' ou 'any'.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "O tipo de retorno de uma função de decorador de propriedade deve ser 'void' ou 'any'.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "O tipo de retorno de uma função assíncrona deve ser uma promessa válida ou não deve conter um membro \"then\" que pode ser chamado.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "O tipo de retorno de uma função assíncrona ou método deve ser o tipo <T>Promessa global.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "O tipo de retorno de uma função assíncrona ou método precisa ser o tipo Promise<T> global. Você quis escrever 'Promise<{0}>'?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "O lado direito de uma instrução 'for...in' deve ser do tipo 'any', um tipo de objeto ou um parâmetro de tipo, mas aqui ele tem o tipo '{0}'.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "O lado direito de uma operação aritmética deve ser do tipo 'any', 'number', 'bigint' ou um tipo enumerado.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "O lado direito de uma expressão 'instanceof' deve ser do tipo 'any', uma classe, uma função ou outro tipo atribuível ao tipo de interface 'Function' ou um tipo de objeto com um método 'Symbol.hasInstance'.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "O lado direito de uma expressão 'instanceof' não deve ser uma expressão de instanciação.", "The_root_value_of_a_0_file_must_be_an_object_5092": "O valor raiz de um arquivo '{0}' precisa ser um objeto.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "O runtime invocará o decorador com argumentos {1}, mas o decorador espera {0}.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "O runtime invocará o decorador com argumentos {1}, mas o decorador espera pelo menos {0}.", "The_shadowing_declaration_of_0_is_defined_here_18017": "A declaração de sombreamento de '{0}' é definida aqui", "The_signature_0_of_1_is_deprecated_6387": "A assinatura '{0}' de '{1}' foi preterida.", "The_specified_path_does_not_exist_Colon_0_5058": "O caminho especificado não existe: '{0}'.", "The_tag_was_first_specified_here_8034": "A marca foi especificada primeiro aqui.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "O destino de uma atribuição REST de objeto pode não ser um acesso opcional de propriedade.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "O destino de uma atribuição rest de objeto deve ser uma variável ou um acesso de propriedade.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "O contexto 'this' de tipo '{0}' não é atribuível para o 'this' do método de tipo '{1}'.", "The_this_types_of_each_signature_are_incompatible_2685": "Os tipos 'this' de cada assinatura são incompatíveis.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "O tipo '{0}' é 'readonly' e não pode ser atribuído ao tipo mutável '{1}'.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "O modificador “type” não pode ser usado em uma exportação nomeada quando “export type” for usado em sua instrução de exportar.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "O modificador “type” não pode ser usado em uma importação nomeada quando “import type” for usado em sua instrução de importar.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "O tipo de uma declaração de função deve corresponder à assinatura da função.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "O tipo deste nó não pode ser serializado porque sua propriedade '{0}' não pode ser serializada.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "O tipo retornado pelo método '{0}()' de um iterador assíncrono deve ser uma promessa para um tipo com a propriedade 'value'.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "O tipo retornado pelo método '{0}()' de um iterador deve ter uma propriedade 'value'.", "The_types_of_0_are_incompatible_between_these_types_2200": "Os tipos de '{0}' são incompatíveis entre esses tipos.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "Os tipos retornados por '{0}' são incompatíveis entre esses tipos.", "The_value_0_cannot_be_used_here_18050": "O valor '{0}' não pode ser usado aqui.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "A declaração de variável de uma instrução 'for...in' não pode ter um inicializador.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "A declaração de variável de uma instrução 'for...of' não pode ter um inicializador.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "A instrução \"with\" não tem suporte. Todos os símbolos em um bloco \"with\" terão o tipo \"any\".", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "Há tipos em '{0}', mas esse resultado não pôde ser resolvido na configuração 'moduleResolution' atual. Considere atualizar para 'node16', 'nodenext' ou 'bundler'.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "Há tipos em '{0}', mas esse resultado não pôde ser resolvido ao respeitar as \"exportações\" do package.json. A biblioteca '{1}' pode precisar atualizar o package.json ou as digitações.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "Não há nenhum grupo de captura chamado '{0}' nesta expressão regular.", "There_is_nothing_available_for_repetition_1507": "Não há nada disponível para repetição.", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "Essa marca JSX requer que \"{0}\" esteja no escopo, mas não foi possível encontrá-la.", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "Essa marca JSX requer a existência do caminho do módulo \"{0}\", mas não foi possível encontrar nenhum. Verifique se você tem os tipos do pacote apropriado instalados.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "A propriedade '{0}' da marca desse JSX espera um único filho do tipo '{1}', mas vários filhos foram fornecidos.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "A propriedade '{0}' da marca desse JSX espera o tipo '{1}' que requer vários filhos, mas somente um único filho foi fornecido.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "Essa referência inversa refere-se a um grupo que não existe. Não há grupos de captura nessa expressão regular.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "Essa referência inversa refere-se a um grupo que não existe. Há apenas {0} grupos de captura nesta expressão regular.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "Essa expressão binária nunca é nula. Está faltando parênteses?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "Este caractere não pode ser escapado em uma expressão regular.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Esta comparação parece não ser intencional porque os tipos '{0}' e '{1}' não têm sobreposição.", "This_condition_will_always_return_0_2845": "Esta condição sempre retornará '{0}'.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Essa condição sempre retornará '{0}', pois o JavaScript compara objetos por referência, não por valor.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Esta condição sempre retornará verdadeiro, já que este '{0}' está sempre definido.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Esta condição sempre retornará verdadeira, uma vez que esta função foi sempre definida. Você pretendia chamá-la em vez disso?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Esta função de construtor pode ser convertida em uma declaração de classe.", "This_expression_is_always_nullish_2871": "Essa expressão sempre é nula.", "This_expression_is_not_callable_2349": "Essa expressão não pode ser chamada.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Esta expressão não pode ser chamada porque é um acessador 'get'. Você quis usá-la sem '()'?", "This_expression_is_not_constructable_2351": "Essa expressão não pode ser construída.", "This_file_already_has_a_default_export_95130": "Este arquivo já tem uma exportação padrão", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "Não é seguro reescrever esse caminho de importação porque ele é resolvido em outro projeto, e o caminho relativo entre os arquivos de saída dos projetos não é o mesmo que o caminho relativo entre seus arquivos de entrada.", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "Essa importação usa uma extensão \"{0}\" para resolver um arquivo TypeScript de entrada, mas não será reescrita durante a emissão porque não é um caminho relativo.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Esta é a declaração que está sendo aumentada. Considere mover a declaração em aumento para o mesmo arquivo.", "This_kind_of_expression_is_always_falsy_2873": "Esse tipo de expressão é sempre inválido.", "This_kind_of_expression_is_always_truthy_2872": "Esse tipo de expressão é sempre verdadeiro.", "This_may_be_converted_to_an_async_function_80006": "<PERSON><PERSON> pode ser convertido em uma função assíncrona.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Este membro não pode ter um comentário JSDoc com uma marca '@override' porque ele não está declarado na classe base '{0}'.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Esse membro não pode ter um comentário JSDoc com uma marca 'override' porque ele não está declarado na classe base '{0}'. Você quis dizer '{1}'?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Este membro não pode ter um comentário JSDoc com uma marca '@override' porque sua classe que contém '{0}' não estende outra classe.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "Esse membro não pode ter um comentário JSDoc com uma marcação '@override' porque seu nome é dinâmico.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Este membro não pode ter um modificador 'override' porque não está declarado na classe base '{0}'.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Esse membro não pode ter um modificador de 'substituição' porque ele não é declarado na classe base '{0}'. Você quis dizer '{1}'?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Este membro não pode ter um modificador 'override' porque a classe que o contém, '{0}', não se estende para outra classe.", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "Este membro não pode ter um modificador 'override' porque seu nome é dinâmico.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Essa membro deve ter um comentário JSDoc com uma marca '@override' porque ela substitui um membro na classe base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Este membro precisa ter um modificador 'override' porque substitui um membro na classe base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Este membro precisa ter um modificador 'override' porque substitui um método abstrato que é declarado na classe base '{0}'.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Esse módulo só pode ser referenciado com importações/exportações de ECMAScript ligando o sinalizador '{0}' e referenciando sua exportação padrão.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Este módulo é declarado com 'export =', e só pode ser usado com uma importação padrão ao usar o sinalizador '{0}'.", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "Essa operação pode ser simplificada. Essa mudança é idêntica a `{0} {1} {2}`.", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "Essa sobrecarga retorna implicitamente o tipo '{0}' porque não tem uma anotação de tipo de retorno.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Esta assinatura de sobrecarga não é compatível com sua assinatura de implementação.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Este parâmetro não é permitido com a diretiva 'use strict'.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "<PERSON><PERSON> propriedade de parâmetro deve ter um comentário JSDoc com uma marca '@override' porque ela substitui um membro na classe base '{0}'.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Esta propriedade de parâmetro deve ter uma modificação de 'substituição' porque substitui um membro na classe base '{0}'.", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "Esse sinalizador de expressão regular não pode ser alternado em um subpadrão.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "Esse sinalizador de expressão regular só está disponível ao direcionar para '{0}' ou posterior.", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "Não é seguro reescrever esse caminho de importação relativo porque ele se parece com um nome de arquivo, mas, na verdade, é resolvido como \"{0}\".", "This_spread_always_overwrites_this_property_2785": "Essa difusão sempre substitui essa propriedade.", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "Esta sintaxe não é permitida quando 'erasableSyntaxOnly' está habilitado.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Essa sintaxe é reservada em arquivos com extensão .mts ou .cts. Adicione uma vírgula final ou restrição explícita.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Essa sintaxe é reservada em arquivos com extensão .mts ou .cts. Em vez disso, use uma expressão `as`.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Essa sintaxe requer um auxiliar importado, mas o módulo '{0}' não pode ser encontrado.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Esta sintaxe requer um auxiliar importado chamado '{1}' que não existe em '{0}'. Considere atualizar sua versão do '{0}'.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Esta sintaxe exige um auxiliar importado nomeado como '{1}' com parâmetros {2}, o que não é compatível com o que está em '{0}'. Considere atualizar sua versão do '{0}'.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Este parâmetro de tipo pode precisar de uma restrição `extends {0}`.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Este uso de 'importar' é inválido. Chamadas 'import()' podem ser escritas, mas devem ter parênteses e não podem ter argumentos de tipo.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Para converter este arquivo em um módulo ECMAScript, adicione o campo `\"type\": \"module\"` a '{0}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Para converter este arquivo em um módulo ECMAScript, altere sua extensão de arquivo para '{0}' ou adicione o campo `\"type\": \"module\"` para '{1}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Para converter este arquivo em um módulo ECMAScript, altere sua extensão de arquivo para '{0}' ou crie um arquivo package.json local com `{ \"type\": \"module\" }`.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Para converter este arquivo em um módulo ECMAScript, crie um arquivo package.json local com `{ \"type\": \"module\" }`.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Expressões 'await' de nível superior só são permitidas quando a opção 'module' estiver definida como 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' ou 'preserve', e a opção 'target' estiver definida como 'es2017' ou superior.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "As instruções 'await' de nível superior só são permitidas quando a opção 'module' está definida como 'es2022', 'esnext', 'system', 'node16', 'node18' ou 'nodenext' e a opção 'target' está definida como 'es2017' ou superior.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "As declarações de nível superior em arquivos .d.ts devem começar com um modificador 'declare' ou 'export'.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Loops 'for await' de nível superior só são permitidos quando a opção 'module' estiver definida como 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' ou 'preserve', e a opção 'target' estiver definida como 'es2017' ou superior.", "Trailing_comma_not_allowed_1009": "Vírgula à direita não permitida.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transcompilar cada arquivo como um módulo separado (do mesmo modo que 'ts.transpileModule').", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Tente `npm i --save-dev @types/{1}` caso exista ou adicione um novo arquivo de declaração (.d.ts) contendo `declare module '{0}';`", "Trying_other_entries_in_rootDirs_6110": "<PERSON><PERSON><PERSON> outras entradas em 'rootDirs'.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "<PERSON><PERSON><PERSON> substituição '{0}', local de módulo candidato: '{1}'.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "O tipo de tupla '{0}' de comprimento '{1}' não tem nenhum elemento no índice '{2}'.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Os argumentos de tipo de tupla se referenciam circularmente.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "O tipo '{0}' só pode ser iterado usando o sinalizador '--downlevelIteration' ou um '--target' igual a 'es2015' ou superior.", "Type_0_cannot_be_used_as_an_index_type_2538": "O tipo '{0}' não pode ser usado como um tipo de índice.", "Type_0_cannot_be_used_to_index_type_1_2536": "O tipo '{0}' não pode ser usado para indexar o tipo '{1}'.", "Type_0_does_not_satisfy_the_constraint_1_2344": "O tipo '{0}' não satisfaz a restrição '{1}'.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "O tipo '{0}' não atende ao tipo esperado '{1}'.", "Type_0_has_no_call_signatures_2757": "O tipo '{0}' não tem assinaturas de chamada.", "Type_0_has_no_construct_signatures_2761": "O tipo '{0}' não tem assinaturas de constructo.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "O tipo '{0}' não tem assinatura de índice correspondente para o tipo '{1}'.", "Type_0_has_no_properties_in_common_with_type_1_2559": "O tipo '{0}' não tem propriedades em comum com o tipo '{1}'.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "O tipo “{0}” não tem assinaturas para as quais a lista de argumentos de tipo é aplicável.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "O tipo '{0}' é genérico e só pode ser indexado para leitura.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "O tipo '{0}' não tem as propriedades a seguir do tipo '{1}': {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "O tipo '{0}' não tem as propriedades a seguir do tipo '{1}': {2} e mais {3}.", "Type_0_is_not_a_constructor_function_type_2507": "O tipo '{0}' não é um tipo de função de construtor.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "O tipo '{0}' não é um tipo de retorno de função assíncrona válido no ES5, pois não se refere ao valor construtor compatível com a Promessa.", "Type_0_is_not_an_array_type_2461": "O tipo '{0}' não é um tipo de matriz.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "O tipo '{0}' não é um tipo de matriz ou de cadeia de caracteres.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "O tipo '{0}' não é um tipo de matriz de um tipo de cadeia ou não tem um método '[Symbol.iterator]()' que retorna um iterador.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "O tipo '{0}' não é um tipo de matriz ou não tem um método '[Symbol.iterator]()' que retorna um iterador.", "Type_0_is_not_assignable_to_type_1_2322": "O tipo '{0}' não pode ser atribuído ao tipo '{1}'.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "O tipo '' não pode ser atribuído ao tipo {0} '{1}'. Você quis dizer '{2}'?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "O tipo '{0}' não é atribuível ao tipo '{1}'. Dois tipos diferentes com esse nome existem, mas eles não estão relacionados.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Tipo '{0}' não é atribuível ao tipo '{1}' como implícito pela anotação de variância.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "O tipo '{0}' não é atribuível ao tipo '{1}', conforme necessário para valores de membro de enumeração computados.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "O tipo '{0}' não pode ser atribuído ao tipo '{1}' com 'exactOptionalPropertyTypes: true'. Considere adicionar 'undefined' aos tipos das propriedades do destino.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "O tipo '{0}' não pode ser atribuído ao tipo '{1}' com 'exactOptionalPropertyTypes: true'. Considere adicionar 'undefined' ao tipo do destino.", "Type_0_is_not_comparable_to_type_1_2678": "O tipo '{0}' não pode ser comparável ao tipo '{1}'.", "Type_0_is_not_generic_2315": "O tipo '{0}' não é genérico.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "O tipo '{0}' pode representar um valor primitivo, que não é permitido como operando direito do operador 'in'.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "O tipo '{0}' deve ter um método '[Symbol.asyncIterator]()' que retorna um iterador assíncrono.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "O tipo '{0}' deve ter um método '[Symbol.iterator]()' que retorna um iterador.", "Type_0_provides_no_match_for_the_signature_1_2658": "O tipo '{0}' fornece nenhuma correspondência para a assinatura '{1}'.", "Type_0_recursively_references_itself_as_a_base_type_2310": "O tipo '{0}' referencia recursivamente a si próprio como um tipo base.", "Type_Checking_6248": "Verificação de Tipo", "Type_alias_0_circularly_references_itself_2456": "O alias de tipo '{0}' referencia circulamente a si próprio.", "Type_alias_must_be_given_a_name_1439": "O alias de tipo deve receber um nome.", "Type_alias_name_cannot_be_0_2457": "O nome do alias de tipo não pode ser '{0}'.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Os aliases de tipo só podem ser usados em arquivos TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Uma anotação de tipo não pode aparecer em uma declaração de construtor.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "As anotações de tipo só podem ser usadas em arquivos TypeScript.", "Type_argument_expected_1140": "Argumento de tipo esperado.", "Type_argument_list_cannot_be_empty_1099": "A lista de argumentos de tipo não pode estar vazia.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Os argumentos de tipo só podem ser usados em arquivos TypeScript.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Os argumentos de tipo '{0}' se referenciam circularmente.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "As expressões de declaração de tipo só podem ser usadas em arquivos TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "O tipo na posição {0} na fonte não é compatível com o tipo na posição {1} no destino.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "O tipo nas posições {0} até {1} na fonte não é compatível com o tipo na posição {2} no destino.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "O tipo que contém o nome particular '{0}' não pode ser usado com --isolatedDeclarations.", "Type_declaration_files_to_be_included_in_compilation_6124": "Arquivos de declaração de tipo a serem incluídos em compilação.", "Type_expected_1110": "Tipo esperado.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "As asserções de importação de tipo devem ter exatamente uma chave - `resolution-mode` - com valor `import` ou` require`.", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "As asserções de importação de tipo devem ter exatamente uma chave - 'resolution-mode' - com valor 'import' ou 'require'.", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "A importação de tipo de um módulo ECMAScript de um módulo CommonJS deve ter um atributo 'resolution-mode'.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "A instanciação de tipo é muito profunda e possivelmente infinita.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "O tipo é referenciado diretamente ou indiretamente em um retorno de chamada de preenchimento do seu próprio método 'then'.", "Type_library_referenced_via_0_from_file_1_1402": "Biblioteca de tipos referenciada via '{0}' do arquivo '{1}'", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Biblioteca de tipos referenciada via '{0}' do arquivo '{1}' com packageId '{2}'", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "O tipo de operando \"await\" deve ser uma promessa válida ou não deve conter um membro \"then\" que pode ser chamado.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "O tipo de valor da propriedade computada é '{0}', que não pode ser atribuído ao tipo '{1}'.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "O tipo de variável '{0}' de membro de instância não pode referenciar o identificador '{1}' declarado no construtor.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "O tipo de elementos iterados de um operando \"yield*\" deve ser uma promessa válida ou não deve conter um membro \"then\" que pode ser chamado.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "O tipo de propriedade '{0}' faz referência circular a si mesmo no tipo mapeado '{1}'.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "O tipo do operando \"yield\" em um gerador assíncrono deve ser uma promessa válida ou não deve conter um membro \"then\" que pode ser chamado.", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "A importação somente de tipo de um módulo ECMAScript de um módulo CommonJS deve ter um atributo 'resolution-mode'.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "O tipo se origina nessa importação. Uma importação de estilo de namespace não pode ser chamada nem construída e causará uma falha no runtime. Considere usar uma importação padrão ou importe require aqui.", "Type_parameter_0_has_a_circular_constraint_2313": "O parâmetro de tipo '{0}' tem uma restrição circular.", "Type_parameter_0_has_a_circular_default_2716": "O parâmetro de tipo '{0}' tem um padrão circular.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "O parâmetro de tipo '{0}' da assinatura de chamada da interface exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "O parâmetro de tipo '{0}' da assinatura de construtor da interface exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "O parâmetro de tipo '{0}' da classe exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "O parâmetro de tipo '{0}' da função exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "O parâmetro de tipo '{0}' da interface exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "O parâmetro de tipo '{0}' do tipo de objeto mapeado exportado tem ou está usando o nome privado '{1}'.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "O parâmetro de tipo '{0}' do alias de tipo exportado tem ou está usando o nome privado '{1}'.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "O parâmetro de tipo '{0}' do método da interface exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "O parâmetro de tipo '{0}' do método público da classe exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "O parâmetro de tipo '{0}' do método estático público da classe exportada tem ou está usando o nome particular '{1}'.", "Type_parameter_declaration_expected_1139": "Declaração de parâmetro de tipo esperada.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "As declarações de parâmetro de tipo só podem ser usadas em arquivos TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Os padrões de parâmetro de tipo só podem referenciar parâmetros de tipo declarados anteriormente.", "Type_parameter_list_cannot_be_empty_1098": "A lista de parâmetros de tipo não pode estar vazia.", "Type_parameter_name_cannot_be_0_2368": "O nome do parâmetro de tipo não pode ser '{0}'.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Os parâmetros de tipo não podem aparecer em uma declaração de construtor.", "Type_predicate_0_is_not_assignable_to_1_1226": "O predicado de tipo '{0}' não pode ser atribuído a '{1}'.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "O tipo produz um tipo de tupla grande demais para ser representado.", "Type_reference_directive_0_was_not_resolved_6120": "======== A diretiva de referência de tipo '{0}' não foi resolvida. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== A diretiva de referência de tipo '{0}' foi resolvida com sucesso para '{1}', primário: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== A diretiva de referência de tipo '{0}' foi resolvida com sucesso para '{1}' com a ID do Pacote '{2}', primário: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "As expressões de satisfação de tipo só podem ser usadas em arquivos TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Os tipos não podem aparecer em declarações de exportação em arquivos JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Tipos têm declarações separadas de uma propriedade privada '{0}'.", "Types_of_construct_signatures_are_incompatible_2419": "Os tipos de assinaturas de constructo são incompatíveis.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Os tipos de parâmetros '{0}' e '{1}' são incompatíveis.", "Types_of_property_0_are_incompatible_2326": "Tipos de propriedade '{0}' são incompatíveis.", "Unable_to_open_file_0_6050": "Não é possível abrir o arquivo '{0}'.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Não é possível resolver a assinatura do decorador de classe quando ele é chamado como uma expressão.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Não é possível resolver a assinatura do decorador de método quando ele é chamado como uma expressão.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Não é possível resolver a assinatura do decorador de parâmetro quando ele é chamado como uma expressão.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Não é possível resolver a assinatura do decorador de propriedade quando ele é chamado como uma expressão.", "Undetermined_character_escape_1513": "Escape de caractere indeterminado.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "'{0}' inesperado. Você quer escapar com barra invertida?", "Unexpected_end_of_text_1126": "Fim inesperado do texto.", "Unexpected_keyword_or_identifier_1434": "Palavra-chave ou identificador inesperado.", "Unexpected_token_1012": "Token inesperado.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Token inesperado. Um construtor, m<PERSON><PERSON><PERSON>, acessador ou propriedade era esperado.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Token inesperado. Era esperado um nome de parâmetro de tipo sem chaves.", "Unexpected_token_Did_you_mean_or_gt_1382": "Token inesperado. Você quis dizer '{'>'}' ou '&gt;'?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Token inesperado. Você quis dizer '{'}'}' ou '&rbrace;'?", "Unexpected_token_expected_1179": "Token inesperado. '{' esperado.", "Unicode_escape_sequence_cannot_appear_here_17021": "A sequência de escape Unicode não pode aparecer aqui.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "As sequências de escape Unicode só estarão disponíveis quando o sinalizador Unicode (u) ou o sinalizador Conjuntos Unicode (v) estiver definido.", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "As expressões de valor da propriedade Unicode só estarão disponíveis quando o sinalizador Unicode (u) ou o sinalizador Conjuntos Unicode (v) estiver definido.", "Unknown_Unicode_property_name_1524": "Nome da propriedade Unicode desconhecido.", "Unknown_Unicode_property_name_or_value_1529": "Nome ou valor da propriedade Unicode desconhecido.", "Unknown_Unicode_property_value_1526": "<PERSON>or da propriedade Unicode desconhecido.", "Unknown_build_option_0_5072": "Opção de build '{0}' desconhecida.", "Unknown_build_option_0_Did_you_mean_1_5077": "Opção de build '{0}' desconhecida. Você quis dizer '{1}'?", "Unknown_compiler_option_0_5023": "Opção do compilador '{0}' desconhecida.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Opção de compilador '{0}' desconhecida. Você quis dizer '{1}'?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Palavra-chave ou identificador desconhecido. Você quis dizer '{0}'?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Opção desconhecida 'excludes'. Você quis dizer 'exclude'?", "Unknown_regular_expression_flag_1499": "Sinalizador de expressão regular desconhecido.", "Unknown_type_acquisition_option_0_17010": "Opção de aquisição de tipo desconhecido '{0}'.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Opção de aquisição de tipo '{0}' desconhecida. Você quis dizer '{1}'?", "Unknown_watch_option_0_5078": "Opção de observador '{0}' desconhecida.", "Unknown_watch_option_0_Did_you_mean_1_5079": "Opção de observador '{0}' desconhecida. Você quis dizer '{1}'?", "Unreachable_code_detected_7027": "Código inacessível detectado.", "Unterminated_Unicode_escape_sequence_1199": "Sequência de escape Unicode não finalizada.", "Unterminated_quoted_string_in_response_file_0_6045": "Cadeia de caracteres entre aspas não finalizada no arquivo de resposta '{0}'.", "Unterminated_regular_expression_literal_1161": "Literal de expressão regular não finalizado.", "Unterminated_string_literal_1002": "Literal de cadeia de caracteres não finalizado.", "Unterminated_template_literal_1160": "Literal de modelo não finalizado.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Chamadas de função não tipadas não podem aceitar argumentos de tipo.", "Unused_label_7028": "Rótulo não utilizado.", "Unused_ts_expect_error_directive_2578": "Diretiva '@ts-expect-error' não usada.", "Update_import_from_0_90058": "Atualizar importação de \"{0}\"", "Update_modifiers_of_0_90061": "Atualizar modificadores de '{0}'", "Updating_output_timestamps_of_project_0_6359": "Atualizando os carimbos de data/hora de saída do projeto '{0}'...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Atualizando os carimbos de data/hora de saída inalterados do projeto '{0}'...", "Use_0_95174": "Use `{0}`.", "Use_0_instead_5106": "Use '{0}' em vez disso.", "Use_Number_isNaN_in_all_conditions_95175": "Use `Number.isNaN` em todas as condições.", "Use_element_access_for_0_95145": "Usar o acesso de elemento para '{0}'", "Use_element_access_for_all_undeclared_properties_95146": "Usar o acesso de elemento para todas as propriedades não declaradas.", "Use_import_type_95180": "Usar 'tipo de importação'", "Use_synthetic_default_member_95016": "Use o membro sintético 'padrão'.", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "Use o campo 'exports' do package.json ao resolver importações de pacote.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "Use o campo 'imports' no package.json ao resolver importações.", "Use_type_0_95181": "Usar 'type {0}'", "Using_0_subpath_1_with_target_2_6404": "<PERSON>and<PERSON> '{0}' subcaminho '{1}' com destino '{2}'.", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "O uso de fragmentos JSX requer que a fábrica de fragmentos \"{0}\" esteja no escopo, mas não foi possível encontrá-la.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Há suporte para o uso de uma cadeia de caracteres em uma instrução 'for...of' somente no ECMAScript 5 e superior.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Usar --build, -b fará com que o tsc se comporte mais como um orquestrador de build do que como um compilador. Isso é usado para acionar a construção de projetos compostos sobre os quais você pode aprender mais em {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "<PERSON><PERSON><PERSON> as opções do compilador de redirecionamento de referência do projeto '{0}'.", "VERSION_6036": "VERSÃO", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "O valor do tipo '{0}' não tem propriedades em comum com o tipo '{1}'. Você queria chamá-lo?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "O valor do tipo '{0}' não pode ser chamado. Você pretendia incluir 'new'?", "Variable_0_implicitly_has_an_1_type_7005": "A variável '{0}' implicitamente tem um tipo '{1}'.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "A variável '{0}' implicitamente tem um tipo '{1}', mas um tipo melhor pode ser inferido do uso.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "A variável '{0}' implicitamente tem o tipo '{1}' em algumas localizações, mas um tipo melhor pode ser inferido do uso.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "A variável '{0}' tem implicitamente o tipo '{1}' em alguns locais onde o tipo não pode ser determinado.", "Variable_0_is_used_before_being_assigned_2454": "A variável '{0}' é usada antes de ser atribuída.", "Variable_declaration_expected_1134": "Declaração de variável esperada.", "Variable_declaration_list_cannot_be_empty_1123": "A lista de declaração de variável não pode estar vazia.", "Variable_declaration_not_allowed_at_this_location_1440": "A declaração de variável não é permitida neste local.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "A variável deve ter uma anotação de tipo explícita com --isolatedDeclarations.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "Variáveis com várias declarações não podem ser embutidas.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "O elemento variádico na posição {0} na fonte não corresponde ao elemento na posição {1} no destino.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Só há suporte para anotações de variação em aliases do tipo para objetos, funções, construtores e tipos mapeados.", "Version_0_6029": "<PERSON><PERSON><PERSON> {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Visite https://aka.ms/tsconfig para ler mais sobre este arquivo", "WATCH_OPTIONS_6918": "OPÇÕES DE INSPEÇÃO", "Watch_and_Build_Modes_6250": "Modos Inspeção e Compilação", "Watch_input_files_6005": "Observe os arquivos de entrada.", "Watch_option_0_requires_a_value_of_type_1_5080": "A opção do observador '{0}' requer um valor do tipo {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Só podemos gravar um tipo de '{0}' adicionando um tipo para o parâmetro inteiro aqui.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Ao atribuir funções, certifique-se que os parâmetros e os valores de retorno sejam compatíveis com subtipo.", "When_type_checking_take_into_account_null_and_undefined_6699": "Quando a fizer a verificação de tipo, considere 'null' e 'undefined'.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Se é necessário manter a saída de console desatualizada no modo de inspeção, em vez de limpar a tela.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Encapsular todos os caracteres inválidos em um contêiner de expressão", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "Encapsular todas as expressões de decorador inválidas entre parênteses", "Wrap_all_object_literal_with_parentheses_95116": "Colocar todo o literal de objeto entre parênteses", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Empacotar todos os JSXs sem pai no fragmento de JSX", "Wrap_in_JSX_fragment_95120": "Encapsular o fragmento de JSX", "Wrap_in_parentheses_95194": "Encapsular entre parênteses", "Wrap_invalid_character_in_an_expression_container_95108": "Encapsular caractere inválido em um contêiner de expressão", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Colocar entre parênteses o corpo a seguir, que deve ser um literal de objeto", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Você pode aprender sobre todas as opções do compilador em {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Não é possível renomear um módulo por meio de uma importação global.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Não é possível renomear elementos definidos em uma pasta 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Não é possível renomear elementos definidos em outra pasta 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Não é possível renomear elementos que são definidos na biblioteca TypeScript padrão.", "You_cannot_rename_this_element_8000": "Você não pode renomear este elemento.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' aceita muito poucos argumentos para serem usados como um decorador aqui. Você quis dizer para chamá-lo primeiro e gravar '@{0}()'?", "_0_and_1_index_signatures_are_incompatible_2330": "As assinaturas de índice '{0}' e '{1}' são incompatíveis.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "As operações '{0}' e '{1}' não podem ser combinadas sem parênteses.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' são especificados duas vezes. O atributo chamado '{0}' será substituído.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "'{0}' no final de um tipo não é uma sintaxe TypeScript válida. Você quis escrever '{1}'?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "'{0}' no início de um tipo não é uma sintaxe TypeScript válida. Você quis escrever '{1}'?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}' só pode ser importado ativando o sinalizador 'esModuleInterop' e usando uma importação padrão.", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}' s<PERSON> pode ser importado usando uma importação padrão.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}' só pode ser importado usando uma chamada 'require' ou ativando o sinalizador 'esModuleInterop' e usando uma importação padrão.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}' só pode ser importado usando uma chamada 'require' ou usando uma importação padrão.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}' só pode ser importado usando 'import {1} = require({2})' ou uma importação padrão.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}' só pode ser importado usando 'import {1} = require({2})' ou ativando o sinalizador 'esModuleInterop' e usando uma importação padrão.", "_0_cannot_be_used_as_a_JSX_component_2786": "O módulo '{0}' não pode ser usado como um componente JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "'{0}' não pode ser usado como um valor porque foi exportado usando 'tipo de exportação'.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "'{0}' não pode ser usado como um valor porque foi importado usando 'tipo de importação'.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Os componentes '{0}' não aceitam texto como elementos filho. O texto em JSX tem o tipo 'cadeia de caracteres', mas o tipo esperado de '{1}' é '{2}'.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "Uma instância de '{0}' poderia ser criada com um tipo arbitrário que poderia não estar relacionado a '{1}'.", "_0_declarations_can_only_be_declared_inside_a_block_1156": "Declarações '{0}' só podem ser declaradas dentro de um bloco.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "As declarações '{0}' só podem ser usadas em arquivos TypeScript.", "_0_declarations_may_not_have_binding_patterns_1492": "Declarações '{0}' podem não ter padrões de associação.", "_0_declarations_must_be_initialized_1155": "Declarações '{0}' devem ser inicializadas.", "_0_expected_1005": "'{0}' esperado.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "'{0}' tem um tipo de cadeia de caracteres, mas deve ter sintaxe de cadeia de caracteres reconhecível sintaticamente quando 'isolatedModules' está habilitado.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{0}' não tem nenhum membro exportado chamado '{1}'. Voc<PERSON> quis dizer '{2}'?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' tem implicitamente um tipo de retorno '{1}', mas um tipo melhor pode ser inferido do uso.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "Implicitamente, '{0}' tem um retorno tipo 'any' porque ele não tem uma anotação de tipo de retorno e é referenciado direta ou indiretamente em uma das suas expressões de retorno.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "Implicitamente, '{0}' tem o tipo 'any' porque não tem uma anotação de tipo e é referenciado direta ou indiretamente em seu próprio inicializador.", "_0_index_signatures_are_incompatible_2634": "'{0}' assinaturas de índice são incompatíveis.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "'{0}' tipo de índice '{1}' não pode ser atribuído a '{2}' tipo de índice '{3}'.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' é um primitivo, mas '{1}' é um objeto de wrapper. Prefira usar '{0}' quando possível.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' é um tipo e não pode ser importado em arquivos JavaScript. Use '{1}' em uma anotação de tipo JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "'{0}' é um tipo e deve ser importado usando uma importação somente de tipo quando 'texttimModuleSyntax' está habilitado.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}' é uma renomeação não usada de '{1}'. Você pretendia usá-lo como uma anotação de tipo?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}' é atribuível à restrição do tipo '{1}', mas é possível criar uma instância de '{1}' com um subtipo diferente de restrição '{2}'.", "_0_is_automatically_exported_here_18044": "'{0}' é exportado automaticamente aqui.", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' é declarado, mas seu valor nunca é lido.", "_0_is_declared_but_never_used_6196": "'{0}' est<PERSON> declarado, mas nunca foi usado.", "_0_is_declared_here_2728": "'{0}' é declarado aqui.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}' está definido como uma propriedade na classe '{1}', mas é substituído aqui em '{2}' como um acessador.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}' está definido como um acessador na classe '{1}', mas é substituído aqui em '{2}' como uma propriedade de instância.", "_0_is_deprecated_6385": "'{0}' foi preterido.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' não é uma metapropriedade para a palavra-chave '{1}'. Você quis dizer '{2}'?", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}' não é permitido como um nome de parâmetro.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' não é permitido como um nome de declaração de variável.", "_0_is_of_type_unknown_18046": "'{0}' é do tipo 'desconhecido'.", "_0_is_possibly_null_18047": "'{0}' é possivelmente 'null'.", "_0_is_possibly_null_or_undefined_18049": "'{0}' é possivelmente 'null' ou 'undefined'.", "_0_is_possibly_undefined_18048": "'{0}' é possivelmente 'indefinido'.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' é referenciado direta ou indiretamente em sua própria expressão base.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' é referenciado direta ou indiretamente em sua própria anotação de tipo.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}' foi especificado mais de uma vez, portanto esse uso será substituído.", "_0_list_cannot_be_empty_1097": "A lista '{0}' não pode estar vazia.", "_0_modifier_already_seen_1030": "O modificador '{0}' já foi visto.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "O modificador '{0}' pode aparecer apenas em um parâmetro de tipo de uma classe, interface ou alias de tipo", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "O modificador '{0}' só pode aparecer em um parâmetro de tipo de uma função, método ou classe", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "O modificador '{0}' não pode aparecer em uma declaração de construtor.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "O modificador '{0}' não pode aparecer em um módulo ou elemento de namespace.", "_0_modifier_cannot_appear_on_a_parameter_1090": "O modificador '{0}' não pode aparecer em um parâmetro.", "_0_modifier_cannot_appear_on_a_type_member_1070": "O modificador '{0}' não pode aparecer em um membro de tipo.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "O modificador '{0}' não pode aparecer em um parâmetro de tipo", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "O modificador '{0}' não pode aparecer em uma declaração 'using'.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "O modificador '{0}' não pode aparecer em uma declaração 'await using'.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "O modificador '{0}' não pode aparecer em uma assinatura de índice.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "O modificador '{0}' não pode aparecer em elementos de classe deste tipo.", "_0_modifier_cannot_be_used_here_1042": "O modificador '{0}' não pode ser usado aqui.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "O modificador '{0}' não pode ser usado em um contexto de ambiente.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "O modificador '{0}' não pode ser usado com um modificador '{1}'.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "O modificador '{0}' não pode ser usado com um identificador privado.", "_0_modifier_must_precede_1_modifier_1029": "O modificador '{0}' deve preceder o modificador '{1}'.", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "'\\{0}' deve ser seguido por uma expressão de valor de propriedade Unicode entre chaves.", "_0_needs_an_explicit_type_annotation_2782": "'{0}' precisa de uma anotação de tipo explícita.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' refere-se apenas a um tipo, mas está sendo usado como um namespace aqui.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' só faz referência a um tipo, mas está sendo usado como valor no momento.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' faz referência somente a um tipo, mas está sendo usado como um valor aqui. Você quis usar '{1} em {0}'?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}' refere-se apenas a um tipo, mas está sendo usado como um valor aqui. Você precisa alterar sua biblioteca de destino? Tente alterar a opção 'lib' do compilador para es2015 ou posterior.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' refere-se a uma UMD global, mas o arquivo atual é um módulo. Considere adicionar uma importação.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' refere-se a um valor, mas está sendo usado como um tipo aqui. Você quis dizer 'typeof {0}'?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "'{0}' resolve para um tipo e deve ser marcado como somente tipo neste arquivo antes de exportar novamente quando '{1}' está habilitado. Considere usar o 'tipo de importação' em que '{0}' é importado.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "'{0}' resolve para um tipo e deve ser marcado como somente tipo neste arquivo antes de exportar novamente quando '{1}' está habilitado. Considere o uso do 'tipo de exportação { {0} como padrão }'.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "'{0}' resolve para uma declaração somente de tipo e deve ser importado usando uma importação somente de tipo quando 'texttimModuleSyntax' está habilitado.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "'{0}' resolve para uma declaração somente de tipo e deve ser marcado como somente tipo nesse arquivo antes de exportar novamente quando '{1}' estiver habilitado. Considere usar o 'tipo de importação' em que '{0}' é importado.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "'{0}' resolve para uma declaração somente de tipo e deve ser marcado como somente tipo nesse arquivo antes de exportar novamente quando '{1}' estiver habilitado. Considere o uso do 'tipo de exportação { {0} como padrão }'.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "'{0}' resolve para uma declaração apenas de tipo e deve ser reexportada usando uma reexportação apenas de tipo quando '{1}' está habilitado.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "'{0}' deve ser colocado dentro do objeto 'compilerOptions' do arquivo config json", "_0_tag_already_specified_1223": "A marca '{0}' já foi especificada.", "_0_was_also_declared_here_6203": "'{0}' também foi declarado aqui.", "_0_was_exported_here_1377": "'{0}' foi exportado aqui.", "_0_was_imported_here_1376": "'{0}' foi importado aqui.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}', que não tem a anotação de tipo de retorno, implicitamente tem um tipo de retorno '{1}'.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "'{0}', que não tem a anotação de tipo de retorno, implicitamente tem um tipo de rendimento '{1}'.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "O modificador 'abstract' pode aparecer somente em uma declaração de classe, método ou propriedade.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "o modificador 'acessador' só pode aparecer em uma declaração de propriedade.", "and_here_6204": "e aqui.", "arguments_cannot_be_referenced_in_property_initializers_2815": "'argumentos' não podem ser referenciados em inicializadores de propriedade.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": trata os arquivos com import, export, import.meta, jsx (com jsx: react-jsx) ou formato esm (com module: node16+) como módulos.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "A expressão 'await' não pode ser usada dentro de um bloco estático de classe.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "As expressões 'await' só são permitidas no nível superior de um arquivo quando esse arquivo é um módulo, mas não tem importações ou exportações. Considere adicionar um 'export {}' vazio para transformar este arquivo em um módulo.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "As expressões 'await' só são permitidas em funções assíncronas e nos níveis superiores de módulos.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "As expressões 'await' não podem ser usadas em inicializadores de parâmetros.", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' não tem efeito sobre o tipo desta expressão.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "As instruções 'for await' só são permitidas no nível superior de um arquivo quando esse arquivo é um módulo, mas este arquivo não tem importações nem exportações. Considere adicionar um 'export {}' vazio para transformar este arquivo em um módulo.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "Instruções 'await using' só são permitidas em funções assíncronas e nos níveis superiores de módulos.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "Instruções 'await using' não podem ser usadas dentro de um bloco estático de classe.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "A opção 'baseUrl' é configurada para '{0}', usando este valor para resolver o nome de módulo não relativo '{1}'.", "c_must_be_followed_by_an_ASCII_letter_1512": "'\\c' deve ser seguido por uma letra ASCII.", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' só pode ser usado no início de um arquivo.", "case_or_default_expected_1130": "'case' ou 'default' esperado.", "catch_or_finally_expected_1472": "é esperado 'catch' ou 'finally'.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "O inicializador de membro de enumeração 'const' foi avaliado como um valor não finito.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "O inicializador de membro de enumeração 'const' foi avaliado como o valor não permitido 'NaN'.", "const_enum_member_initializers_must_be_constant_expressions_2474": "Os inicializadores de membro de enumeração const devem ser expressões constantes.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Enumerações 'const' só podem ser usadas em expressões de acesso de índice ou propriedade, ou então do lado direito de uma consulta de tipo, declaração de importação ou atribuição de exportação.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "Não é possível usar 'constructor' como nome de uma propriedade de parâmetro.", "constructor_is_a_reserved_word_18012": "'#constructor' é uma palavra reservada.", "default_Colon_6903": "padrão:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "'delete' não pode ser chamado em um identificador no modo estrito.", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' não exporta novamente um padrão.", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' só pode ser usado em arquivos TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "O modificador 'export' não pode ser aplicado a módulos de ambiente e acréscimos de módulo, pois eles estão sempre visíveis.", "extends_clause_already_seen_1172": "A cláusula 'extends' já foi vista.", "extends_clause_must_precede_implements_clause_1173": "A cláusula 'extends' deve preceder a cláusula 'implements'.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "A cláusula 'extends' da classe exportada '{0}' tem ou está usando o nome particular '{1}'.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "A cláusula 'extends' da classe exportada tem ou está usando o nome particular '{0}'.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "A cláusula 'extends' da interface exportada '{0}' tem ou está usando o nome particular '{1}'.", "false_unless_composite_is_set_6906": "`false`, a menos que `composite` esteja definido", "false_unless_strict_is_set_6905": "`false`, a menos que `strict` esteja definido", "file_6025": "arquivo", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "Os loops 'for await' só são permitidos no nível superior de um arquivo quando esse arquivo é um módulo, mas este arquivo não tem importações nem exportações. Considere adicionar um 'export {}' vazio para transformar este arquivo em um módulo.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "Os loops 'for await' só são permitidos em funções assíncronas e nos níveis superiores dos módulos.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "<PERSON><PERSON> loops 'for await' não podem ser usados dentro de um bloco estático de classe.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "os acessadores 'get' e 'set' não podem declarar os parâmetros 'this'.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "`[]` se `files` for especificado, caso contrário `[\"**/*\"]5D;`", "implements_clause_already_seen_1175": "A cláusula 'implements' já foi vista.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Cláusulas 'implements' só podem ser usadas em arquivos TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' só pode ser usado em arquivos TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "As declarações 'infer' só são permitidas na cláusula 'extends' de um tipo condicional.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "'\\k' deve ser seguido por um nome de grupo de captura entre colchetes angulares.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "O uso de 'let' não é permitido como um nome em declarações 'let' ou 'const'.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "módulo === `AMD` ou `UMD` ou `System` ou `ES6` e `Classic`. <PERSON><PERSON><PERSON> con<PERSON>, `Node`", "module_system_or_esModuleInterop_6904": "módulo === \"system\" ou esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "A expressão 'new', cujo destino não tem uma assinatura de constructo, implicitamente tem um tipo 'any'.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\" node_modules \",\" bower_components \",\" jspm_packages \"]`, mais o valor de `outDir`, caso seja especificado.", "one_of_Colon_6900": "um dos:", "one_or_more_Colon_6901": "um ou mais:", "options_6024": "opções", "or_JSX_element_expected_1145": "'{' ou elemento JSX esperado.", "or_expected_1144": "'{' ou ';' esperado.", "package_json_does_not_have_a_0_field_6100": "'package.json' não tem um campo '{0}'.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json' não tem uma entrada 'typesVersions' que corresponda à versão '{0}'.", "package_json_had_a_falsy_0_field_6220": "'package.json' teve um campo '{0}' false.", "package_json_has_0_field_1_that_references_2_6101": "'package.json' tem '{0}' campo '{1}' que faz referência a '{2}'.", "package_json_has_a_peerDependencies_field_6281": "'package.json' tem um campo 'peerDependencies'.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json' tem uma entrada 'typesVersions' '{0}' que não é um intervalo semver válido.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json' tem uma entrada 'typesVersions' '{0}' que corresponde à versão do compilador '{1}', procurando por um padrão que corresponda ao nome do módulo '{2}'.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json' tem um campo 'typesVersions' com mapeamentos de caminho específicos à versão.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "O escopo package.json '{0}' mapeia explicitamente o especificador '{1}' para nulo.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "O escopo package.json '{0}' tem um tipo inválido para o destino do especificador '{1}'", "package_json_scope_0_has_no_imports_defined_6273": "O escopo package.json '{0}' não tem importações definidas.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "A opção 'paths' é especificada, procurando por um padrão para corresponder ao nome do módulo '{0}'.", "q_is_only_available_inside_character_class_1511": "'\\q' só está disponível dentro da classe de caracteres.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "'\\q' deve ser seguido por alternativas de cadeia de caracteres entre chaves.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "O modificador 'readonly' pode aparecer somente em uma declaração de propriedade ou assinatura de índice.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "O modificador de tipo 'readonly' só é permitido em tipos literais de matriz e tupla.", "require_call_may_be_converted_to_an_import_80005": "A chamada 'require' pode ser convertida em uma importação.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "`resolution-mode` pode ser definido apenas para importações somente de tipo.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "`resolution-mode` é a única chave válida para as asserções de importação de tipo.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "'resolution-mode' é a única chave válida para os atributos de importação de tipo.", "resolution_mode_should_be_either_require_or_import_1453": "'resolution-mode' deve ser 'require' ou 'import'.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "A opção 'rootDirs' está configurada, usando-a para resolver o nome de módulo relativo '{0}'.", "super_can_only_be_referenced_in_a_derived_class_2335": "'super' s<PERSON> pode ser referenciado em uma classe derivada.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "'super' pode ser referido somente em membros de classes derivadas ou expressões literais de objeto.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "'super' não pode ser referenciado em um nome de propriedade calculado.", "super_cannot_be_referenced_in_constructor_arguments_2336": "'super' não pode ser referenciado nos argumentos do construtor.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "'super' é permitido somente em membros de expressões literais de objeto quando a opção 'target' é 'ES2015' ou maior.", "super_may_not_use_type_arguments_2754": "'super' não pode usar argumentos de tipo.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "'super' deve ser chamado antes de acessar uma propriedade de 'super' no construtor de uma classe derivada.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "'super' deve ser chamado antes de acessar 'this' no construtor de uma classe derivada.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' deve ser seguido por um acesso de membro ou lista de argumentos.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "O acesso à propriedade 'super' só é permitido em um construtor, em funções de membros ou acessadores de membros de uma classe derivada.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "'this' não pode ser referenciado em um nome de propriedade calculado.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "'this' não pode ser referenciado em um corpo de módulo ou de namespace.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "'this' não pode ser referenciado em um inicializador de propriedade estática.", "this_cannot_be_referenced_in_current_location_2332": "'this' não pode ser referenciado no local atual.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' implicitamente tem o tipo 'any' porque não tem uma anotação de tipo.", "true_for_ES2022_and_above_including_ESNext_6930": "'True' para ES2022 e acima, incluindo ESNext.", "true_if_composite_false_otherwise_6909": "`true` se` composite`, `false` caso contr<PERSON>rio", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "'true' quando 'moduleResolution' é 'node16', 'nodenext' ou 'bundler'; caso contrário, \"false\".", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: o Compilador TypeScript", "type_Colon_6902": "tipo:", "unique_symbol_types_are_not_allowed_here_1335": "Tipos de 'unique symbol' não são permitidos aqui.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Tipos de 'unique symbol' são permitidos apenas em variáveis em uma declaração de variável.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Tipos de 'unique symbol' não podem ser usados em uma declaração de variável com um nome associado.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "A diretiva 'use strict' não pode ser usada com uma lista de parâmetros não simples.", "use_strict_directive_used_here_1349": "A diretiva 'use strict' usada aqui.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "As declarações 'with' não são permitidas em blocos de funções assíncronas.", "with_statements_are_not_allowed_in_strict_mode_1101": "Instruções 'with' não são permitidas no modo estrito.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Expressão 'yield' resulta implicitamente em um tipo 'any' porque seu gerador contido não tem uma anotação de tipo de retorno.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "As expressões 'yield' não podem ser usadas em inicializadores de parâmetros."}