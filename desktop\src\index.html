<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Clonie - High-Fidelity Voice Cloning</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f9fafb;
    }

    #root {
      width: 100%;
      height: 100vh;
    }

    /* Loading screen */
    .loading-screen {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .loading-subtext {
      font-size: 14px;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div id="root">
    <!-- Loading screen shown while React app loads -->
    <div class="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading Clonie Desktop</div>
      <div class="loading-subtext">Initializing voice cloning engine...</div>
    </div>
  </div>

  <!-- React app will be injected here -->
  <script>
    // Remove loading screen once React app is ready
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const loadingScreen = document.querySelector('.loading-screen');
        if (loadingScreen) {
          loadingScreen.style.opacity = '0';
          loadingScreen.style.transition = 'opacity 0.5s ease-out';
          setTimeout(() => {
            loadingScreen.remove();
          }, 500);
        }
      }, 2000); // Show loading for at least 2 seconds
    });
  </script>
</body>
</html>
