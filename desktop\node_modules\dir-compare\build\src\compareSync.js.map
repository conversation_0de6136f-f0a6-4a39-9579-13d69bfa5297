{"version": 3, "file": "compareSync.js", "sourceRoot": "", "sources": ["../../src/compareSync.js"], "names": [], "mappings": "AAAA,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AACxB,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;AACjC,MAAM,YAAY,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAA;AACpD,MAAM,aAAa,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAA;AACtD,MAAM,KAAK,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAA;AACtD,MAAM,YAAY,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAA;AACtD,MAAM,eAAe,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAA;AAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAA;AAC9C,MAAM,EAAE,qCAAqC,EAAE,sCAAsC,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAA;AAElK;;GAEG;AACH,SAAS,UAAU,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO;IAC9D,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;QAC5B,OAAO,EAAE,CAAA;KACZ;IACD,IAAI,SAAS,CAAC,WAAW,EAAE;QACvB,IAAI,SAAS,CAAC,kBAAkB,EAAE;YAC9B,OAAO,EAAE,CAAA;SACZ;QACD,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;QACtD,OAAO,YAAY,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;KACjF;IACD,OAAO,CAAC,SAAS,CAAC,CAAA;AACtB,CAAC;AAED;;GAEG;AACH,SAAS,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY;IACpG,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IAC5E,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IAC5E,YAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;IAEnG,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,CAAA;IAC7E,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,CAAA;IAC7E,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;IAClB,OAAO,EAAE,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;QACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3B,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3B,IAAI,KAAK,EAAE,KAAK,CAAA;QAEhB,gCAAgC;QAChC,IAAI,GAAG,CAAA;QACP,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC9C,GAAG,GAAG,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;YAC3D,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;SACpC;aAAM,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC7B,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpC,GAAG,GAAG,CAAC,CAAC,CAAA;SACX;aAAM;YACH,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,GAAG,GAAG,CAAC,CAAA;SACV;QAED,gBAAgB;QAChB,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,wDAAwD;YACxD,IAAI,IAAI,EAAE,MAAM,EAAE,KAAK,CAAA;YACvB,MAAM,qBAAqB,GAAG,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEtE,IAAI,qBAAqB,KAAK,WAAW,EAAE;gBACvC,MAAM,eAAe,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;gBACtF,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAA;gBACnD,IAAI,GAAG,eAAe,CAAC,IAAI,CAAA;gBAC3B,MAAM,GAAG,eAAe,CAAC,MAAM,CAAA;aAClC;iBAAM;gBACH,KAAK,GAAG,UAAU,CAAA;gBAClB,IAAI,GAAG,KAAK,CAAA;gBACZ,MAAM,GAAG,mBAAmB,CAAA;aAC/B;YAGD,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAA;YAC9H,KAAK,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YAC3G,EAAE,EAAE,CAAA;YACJ,EAAE,EAAE,CAAA;YACJ,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,KAAK,WAAW,EAAE;gBAC/C,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aAC5J;SACJ;aAAM,IAAI,GAAG,GAAG,CAAC,EAAE;YAChB,gBAAgB;YAChB,MAAM,qBAAqB,GAAG,sCAAsC,CAAC,MAAM,CAAC,CAAA;YAC5E,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAA;YACrI,KAAK,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACrF,EAAE,EAAE,CAAA;YACJ,IAAI,KAAK,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC/C,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aAC/J;SACJ;aAAM;YACH,eAAe;YACf,IAAI,qBAAqB,GAAG,qCAAqC,CAAC,MAAM,CAAC,CAAA;YACzE,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAA;YACtI,KAAK,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACtF,EAAE,EAAE,CAAA;YACJ,IAAI,KAAK,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC/C,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;aAC/J;SACJ;KACJ;AACL,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA"}