@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                    Starting Clonie Web App                   ║
echo ╠═══════════════════════════════════════════════════════════════╣
echo ║   🎙️  High-Fidelity Voice Cloning Studio                    ║
echo ║   🚀  React Frontend + Python FastAPI Backend               ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

echo 📦 Installing backend dependencies...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo 🚀 Starting backend server...
cd ..\backend
start "Clonie Backend" cmd /k "python main.py"

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 Starting frontend development server...
cd ..\frontend
start "Clonie Frontend" cmd /k "npm run dev"

echo.
echo ✅ Clonie is starting up!
echo.
echo 📱 Frontend will be available at: http://localhost:5173
echo 🔧 Backend API will be available at: http://localhost:8000
echo.
echo 💡 Both servers are running in separate windows.
echo    Close those windows to stop the servers.
echo.
pause
