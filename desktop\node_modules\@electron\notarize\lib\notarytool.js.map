{"version": 3, "file": "notarytool.js", "sourceRoot": "", "sources": ["../src/notarytool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,2CAA6B;AAE7B,mCAAgC;AAChC,uCAAoD;AACpD,mDAIyB;AAGzB,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,8BAA8B,CAAC,CAAC;AAEhD,SAAS,iBAAiB,CAAC,OAA8B;IACvD,MAAM,IAAI,GAAG,IAAA,mDAAmC,EAAC,OAAO,CAAC,CAAC;IAC1D,IAAI,IAAA,+CAA+B,EAAC,IAAI,CAAC,EAAE;QACzC,OAAO;YACL,YAAY;YACZ,IAAA,oBAAU,EAAC,IAAI,CAAC,OAAO,CAAC;YACxB,YAAY;YACZ,IAAA,oBAAU,EAAC,IAAI,CAAC,eAAe,CAAC;YAChC,WAAW;YACX,IAAA,oBAAU,EAAC,IAAI,CAAC,MAAM,CAAC;SACxB,CAAC;KACH;SAAM,IAAI,IAAA,6CAA6B,EAAC,IAAI,CAAC,EAAE;QAC9C,OAAO;YACL,OAAO;YACP,IAAA,oBAAU,EAAC,IAAI,CAAC,WAAW,CAAC;YAC5B,UAAU;YACV,IAAA,oBAAU,EAAC,IAAI,CAAC,aAAa,CAAC;YAC9B,UAAU;YACV,IAAA,oBAAU,EAAC,IAAI,CAAC,cAAc,CAAC;SAChC,CAAC;KACH;SAAM;QACL,0FAA0F;QAC1F,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAClF;QACD,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;KACrD;AACH,CAAC;AAED,SAAsB,qBAAqB;;QACzC,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;QAC9D,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;IAC3B,CAAC;CAAA;AAHD,sDAGC;AAED,SAAsB,4BAA4B,CAAC,IAA4B;;QAC7E,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,MAAM,IAAA,qBAAW,EAAC,CAAM,GAAG,EAAC,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;YAC1E,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAA,aAAK,EAC3B,OAAO,EACP,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,EACrF;gBACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAChC,CACF,CAAC;YACF,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CACb,gDAAgD,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,MAAM,EAAE,CACxF,CAAC;aACH;YACD,CAAC,CAAC,8CAA8C,CAAC,CAAC;YAElD,MAAM,YAAY,GAAG;gBACnB,YAAY;gBACZ,QAAQ;gBACR,OAAO;gBACP,GAAG,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,QAAQ;gBACR,iBAAiB;gBACjB,MAAM;aACP,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhD,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;gBACvE,IAAI;oBACF,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE;wBACvB,MAAM,SAAS,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE;4BACrC,YAAY;4BACZ,KAAK;4BACL,MAAM,CAAC,EAAE;4BACT,GAAG,iBAAiB,CAAC,IAAI,CAAC;yBAC3B,CAAC,CAAC;wBACH,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;qBACzC;iBACF;gBAAC,OAAO,CAAC,EAAE;oBACV,CAAC,CAAC,kCAAkC,EAAE,CAAC,CAAC,CAAC;iBAC1C;gBACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;aAC1E;YACD,CAAC,CAAC,sBAAsB,CAAC,CAAC;QAC5B,CAAC,CAAA,CAAC,CAAC;IACL,CAAC;CAAA;AAlDD,oEAkDC"}