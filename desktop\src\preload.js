const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Backend communication
  getBackendPort: () => ipcRenderer.invoke('get-backend-port'),

  // File dialogs
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),

  // Menu events
  onMenuOpenFile: (callback) => ipcRenderer.on('menu-open-file', callback),

  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  platform: process.platform,
  isElectron: true,

  // File drag and drop support
  onFileDrop: (callback) => {
    window.addEventListener('electron-file-drop', callback);
  },

  removeFileDropListener: (callback) => {
    window.removeEventListener('electron-file-drop', callback);
  }
});

console.log('Preload script loaded');
