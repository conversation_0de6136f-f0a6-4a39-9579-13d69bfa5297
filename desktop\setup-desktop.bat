@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                 Clonie Desktop Setup Wizard                  ║
echo ╠═══════════════════════════════════════════════════════════════╣
echo ║  This script will set up the Clonie desktop development      ║
echo ║  environment and optionally build the production installer.  ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 Checking prerequisites...

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
)

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.8+ from https://python.org/
    pause
    exit /b 1
) else (
    echo ✅ Python found
)

:: Check pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip not found. Please ensure pip is installed with Python
    pause
    exit /b 1
) else (
    echo ✅ pip found
)

echo.
echo 📦 Installing desktop dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install desktop dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing backend dependencies...
cd ..\backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

cd ..\desktop

echo.
echo ✅ Setup completed successfully!
echo.
echo 🎯 What would you like to do next?
echo.
echo [1] Test in development mode (recommended first)
echo [2] Build production installer
echo [3] Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🚀 Starting development mode...
    echo    This will build the frontend and start Electron
    echo    The Python backend will run from source code
    echo.
    pause
    call scripts\dev.bat
) else if "%choice%"=="2" (
    echo.
    echo 🏗️ Building production installer...
    echo    This will take several minutes and create a ~200MB installer
    echo    Make sure you have at least 5GB free disk space
    echo.
    set /p confirm="Are you sure? This will take 10-20 minutes (y/N): "
    if /i "%confirm%"=="y" (
        call scripts\build-all.bat
    ) else (
        echo Build cancelled.
    )
) else (
    echo.
    echo 👋 Setup complete! You can run these commands later:
    echo.
    echo    Development mode:  desktop\scripts\dev.bat
    echo    Production build:  desktop\scripts\build-all.bat
    echo.
)

echo.
echo 📚 Next steps:
echo    - Read desktop\README.md for detailed information
echo    - Check desktop\INSTALLATION.md for installation guide
echo    - Review desktop\package.json for available scripts
echo.
pause
