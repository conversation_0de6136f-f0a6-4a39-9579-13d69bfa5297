import React, { useState, useEffect } from 'react';
import { Mic, Volume2, Setting<PERSON>, Info, Monitor } from 'lucide-react';
import VoiceCloneSection from '../../frontend/src/components/VoiceCloneSection';
import TTSSection from '../../frontend/src/components/TTSSection';
import StatusDisplay from '../../frontend/src/components/StatusDisplay';
import TipsAccordion from '../../frontend/src/components/TipsAccordion';
import ElectronVoiceCloningAPI, { initializeAPI } from './electron-api';

interface AppState {
  isSystemReady: boolean;
  systemStatus: string;
  currentVoiceInfo: string | null;
  sampleAudioId: string | null;
  isElectron: boolean;
}

function ElectronApp() {
  const [state, setState] = useState<AppState>({
    isSystemReady: false,
    systemStatus: 'Initializing desktop application...',
    currentVoiceInfo: null,
    sampleAudioId: null,
    isElectron: ElectronVoiceCloningAPI.isElectron(),
  });

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize API with dynamic port
      await initializeAPI();
      
      // Check system health
      await checkSystemHealth();
      
      // Set up menu listeners if in Electron
      if (window.electronAPI) {
        window.electronAPI.onMenuOpenFile(() => {
          // Trigger file open dialog
          console.log('Menu: Open file requested');
        });
      }
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setState(prev => ({
        ...prev,
        isSystemReady: false,
        systemStatus: 'Failed to initialize application. Please restart.'
      }));
    }
  };

  const checkSystemHealth = async () => {
    try {
      setState(prev => ({
        ...prev,
        systemStatus: 'Connecting to voice cloning engine...'
      }));

      const health = await ElectronVoiceCloningAPI.healthCheck();
      setState(prev => ({
        ...prev,
        isSystemReady: health.voice_system_ready,
        systemStatus: health.voice_system_ready 
          ? 'Desktop application ready for voice cloning' 
          : 'Voice cloning engine initializing...'
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isSystemReady: false,
        systemStatus: 'Voice cloning engine not available. Please wait for initialization.'
      }));
    }
  };

  const handleVoiceCloned = (voiceInfo: string, sampleAudioId: string) => {
    setState(prev => ({
      ...prev,
      currentVoiceInfo: voiceInfo,
      sampleAudioId: sampleAudioId,
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="gradient-bg text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Mic className="w-10 h-10 sm:w-12 sm:h-12 mr-3 sm:mr-4" />
              <h1 className="text-3xl sm:text-4xl font-bold">Clonie</h1>
              {state.isElectron && (
                <div className="ml-3 flex items-center bg-blue-600 bg-opacity-30 px-3 py-1 rounded-full">
                  <Monitor className="w-4 h-4 mr-1" />
                  <span className="text-sm font-medium">Desktop</span>
                </div>
              )}
            </div>
            <p className="text-lg sm:text-xl text-blue-100">
              High-Fidelity Voice Cloning Studio
            </p>
            <p className="text-sm sm:text-base text-blue-200 mt-2">
              Professional voice cloning with pristine audio quality
            </p>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-4">
        <StatusDisplay
          isReady={state.isSystemReady}
          status={state.systemStatus}
          onRetry={checkSystemHealth}
        />
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 pb-12">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
          {/* Voice Cloning Section */}
          <div className="space-y-6">
            <div className="card">
              <div className="flex items-center mb-6">
                <Volume2 className="w-6 h-6 mr-3 text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-800">
                  Step 1: Upload Voice Sample
                </h2>
              </div>
              
              <VoiceCloneSection
                isSystemReady={state.isSystemReady}
                onVoiceCloned={handleVoiceCloned}
                currentVoiceInfo={state.currentVoiceInfo}
                sampleAudioId={state.sampleAudioId}
              />
            </div>
          </div>

          {/* TTS Generation Section */}
          <div className="space-y-6">
            <div className="card">
              <div className="flex items-center mb-6">
                <Settings className="w-6 h-6 mr-3 text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-800">
                  Step 2: Generate Speech
                </h2>
              </div>
              
              <TTSSection
                isSystemReady={state.isSystemReady}
                hasVoice={!!state.currentVoiceInfo}
              />
            </div>
          </div>
        </div>

        {/* Tips Section */}
        <div className="mt-12">
          <TipsAccordion />
        </div>

        {/* Footer */}
        <div className="mt-16 text-center text-gray-500">
          <div className="flex items-center justify-center mb-2">
            <Info className="w-4 h-4 mr-2" />
            <span className="text-sm">
              Powered by XTTS v2 • Built with React & Electron
            </span>
          </div>
          <p className="text-xs">
            High-fidelity voice cloning desktop application
          </p>
          {state.isElectron && (
            <p className="text-xs mt-1 text-blue-600">
              Platform: {ElectronVoiceCloningAPI.getPlatform()} • Version: 1.0.0
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default ElectronApp;
