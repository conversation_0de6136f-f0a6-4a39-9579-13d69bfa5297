@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║              Building Clonie Desktop Application              ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0.."

echo 🧹 Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "backend-dist" rmdir /s /q "backend-dist"
if exist "release" rmdir /s /q "release"
if exist "backend-build\dist" rmdir /s /q "backend-build\dist"
if exist "backend-build\build" rmdir /s /q "backend-build\build"

echo.
echo 📦 Installing desktop dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install desktop dependencies
    pause
    exit /b 1
)

echo.
echo 🔧 Building React frontend...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo ❌ Frontend build failed
    pause
    exit /b 1
)

echo.
echo 📁 Copying frontend build to desktop...
cd ..\desktop
if not exist "dist" mkdir "dist"
xcopy /E /I /Y "..\frontend\dist\*" "dist\"

echo.
echo 🐍 Installing backend dependencies...
cd ..\backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing PyInstaller...
pip install pyinstaller

echo.
echo 🔧 Building backend executable...
cd ..\desktop\backend-build
pyinstaller --clean main.spec
if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    pause
    exit /b 1
)

echo.
echo 📁 Copying backend executable...
cd ..
if not exist "backend-dist" mkdir "backend-dist"
copy "backend-build\dist\clonie-backend.exe" "backend-dist\main.exe"

echo.
echo 🏗️ Building Electron application...
call npm run dist
if %errorlevel% neq 0 (
    echo ❌ Electron build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo 📦 Installer created in: desktop\release\
echo 🚀 You can now distribute the installer to users
echo.

dir release\*.exe

echo.
pause
