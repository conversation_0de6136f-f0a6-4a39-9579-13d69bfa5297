@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║              Building Clonie Desktop Application              ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0.."

echo 🧹 Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "backend-dist" rmdir /s /q "backend-dist"
if exist "release" rmdir /s /q "release"
if exist "backend-build\dist" rmdir /s /q "backend-build\dist"
if exist "backend-build\build" rmdir /s /q "backend-build\build"

echo.
echo 📦 Installing desktop dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install desktop dependencies
    pause
    exit /b 1
)

echo.
echo 🔧 Building React frontend...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

call npm run build
if %errorlevel% neq 0 (
    echo ❌ Frontend build failed
    pause
    exit /b 1
)

echo.
echo 📁 Copying frontend build to desktop...
cd ..\desktop
if exist "dist" rmdir /s /q "dist"
mkdir "dist"
xcopy /E /I /Y "..\frontend\dist\*" "dist\"

echo.
echo 🔧 Updating frontend for Electron...
:: The frontend build is already complete and ready for Electron

echo.
echo 🐍 Installing backend dependencies...
cd ..\backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing PyInstaller...
pip install pyinstaller

echo.
echo 🔧 Building backend executable...
cd ..\desktop\backend-build
pyinstaller --clean main.spec
if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    pause
    exit /b 1
)

echo.
echo 📁 Copying backend executable...
cd ..
if not exist "backend-dist" mkdir "backend-dist"
copy "backend-build\dist\clonie-backend.exe" "backend-dist\main.exe"

echo.
echo 🏗️ Building Electron application...
call npm run dist
if %errorlevel% neq 0 (
    echo ❌ Electron build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo 📦 Windows Installer Created:
dir release\*.exe 2>nul
if exist "release\Clonie-Setup-*.exe" (
    echo.
    echo 🎉 SUCCESS! Your installer is ready:
    for %%f in (release\Clonie-Setup-*.exe) do echo    📁 %%f
    echo.
    echo 🚀 NEXT STEPS:
    echo    1. Double-click the installer to test installation
    echo    2. The installer will create a "Clonie" program you can launch
    echo    3. Distribute this .exe file to users
    echo    4. Users just double-click to install - no technical knowledge needed!
    echo.
    echo 💡 The installer includes:
    echo    - Complete Clonie application
    echo    - Python backend (no separate install needed)
    echo    - Desktop and Start Menu shortcuts
    echo    - Automatic uninstaller
) else (
    echo ❌ No installer found. Check for build errors above.
)

echo.
pause
