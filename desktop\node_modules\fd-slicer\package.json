{"name": "fd-slicer", "version": "1.1.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec --check-leaks", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --timeout 10000 --reporter spec --check-leaks test/test.js"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"istanbul": "~0.3.3", "mocha": "~2.0.1", "stream-equal": "~0.1.5", "streamsink": "~1.2.0"}, "dependencies": {"pend": "~1.2.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "keywords": ["createReadStream", "createWriteStream"]}