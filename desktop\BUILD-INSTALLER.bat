@echo off
title <PERSON><PERSON>ie Desktop - Build Windows Installer

echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                 🚀 BUILD CLONIE INSTALLER 🚀                 ║
echo ╠═══════════════════════════════════════════════════════════════╣
echo ║                                                               ║
echo ║  This will create a Windows installer (.exe) that users      ║
echo ║  can double-click to install <PERSON><PERSON><PERSON> on their computer.       ║
echo ║                                                               ║
echo ║  ⏱️  Build time: 10-20 minutes                                ║
echo ║  💾  Disk space needed: ~5GB                                 ║
echo ║  📦  Final installer size: ~200MB                            ║
echo ║                                                               ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 Checking if this is the right directory...
if not exist "package.json" (
    echo ❌ Error: package.json not found
    echo    Make sure you're running this from the desktop folder
    pause
    exit /b 1
)

if not exist "..\frontend" (
    echo ❌ Error: Frontend folder not found
    echo    Make sure the frontend folder exists in the parent directory
    pause
    exit /b 1
)

if not exist "..\backend" (
    echo ❌ Error: Backend folder not found  
    echo    Make sure the backend folder exists in the parent directory
    pause
    exit /b 1
)

echo ✅ Directory structure looks good!
echo.

echo 🤔 Ready to build the installer?
echo.
echo    This will:
echo    ✓ Install all dependencies
echo    ✓ Build the React frontend
echo    ✓ Bundle the Python backend
echo    ✓ Create Windows installer (.exe)
echo.
set /p confirm="Continue? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Build cancelled.
    pause
    exit /b 0
)

echo.
echo 🚀 Starting build process...
echo.

call scripts\build-all.bat

echo.
echo 🎯 Build process completed!
echo.

if exist "release\Clonie-Setup-*.exe" (
    echo ╔═══════════════════════════════════════════════════════════════╗
    echo ║                        🎉 SUCCESS! 🎉                        ║
    echo ╠═══════════════════════════════════════════════════════════════╣
    echo ║                                                               ║
    echo ║  Your Windows installer has been created!                    ║
    echo ║                                                               ║
    
    for %%f in (release\Clonie-Setup-*.exe) do (
        echo ║  📁 File: %%~nxf
        echo ║  📍 Location: desktop\release\
        echo ║  📏 Size: %%~zf bytes
    )
    
    echo ║                                                               ║
    echo ║  🚀 WHAT TO DO NEXT:                                         ║
    echo ║                                                               ║
    echo ║  1. Test the installer:                                      ║
    echo ║     • Double-click the .exe file                            ║
    echo ║     • Follow the installation wizard                        ║
    echo ║     • Launch Clonie from Start Menu or Desktop              ║
    echo ║                                                               ║
    echo ║  2. Distribute to users:                                     ║
    echo ║     • Send them the .exe file                               ║
    echo ║     • They just double-click to install                     ║
    echo ║     • No technical knowledge required!                      ║
    echo ║                                                               ║
    echo ╚═══════════════════════════════════════════════════════════════╝
    
    echo.
    set /p open="Open the release folder now? (Y/N): "
    if /i "%open%"=="Y" (
        explorer release
    )
) else (
    echo ╔═══════════════════════════════════════════════════════════════╗
    echo ║                        ❌ BUILD FAILED                       ║
    echo ╠═══════════════════════════════════════════════════════════════╣
    echo ║                                                               ║
    echo ║  The installer was not created successfully.                 ║
    echo ║  Please check the error messages above.                      ║
    echo ║                                                               ║
    echo ║  Common issues:                                               ║
    echo ║  • Missing Node.js or Python                                ║
    echo ║  • Insufficient disk space                                   ║
    echo ║  • Antivirus blocking the build                             ║
    echo ║  • Network issues downloading dependencies                   ║
    echo ║                                                               ║
    echo ╚═══════════════════════════════════════════════════════════════╝
)

echo.
pause
