@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                Building Clonie Backend Executable            ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0.."

echo 📦 Installing PyInstaller...
pip install pyinstaller

echo.
echo 🔧 Building backend executable...
cd backend-build
pyinstaller --clean main.spec

if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    pause
    exit /b 1
)

echo.
echo 📁 Copying executable to desktop directory...
if not exist "..\backend-dist" mkdir "..\backend-dist"
copy "dist\clonie-backend.exe" "..\backend-dist\main.exe"

echo.
echo ✅ Backend executable built successfully!
echo    Location: desktop\backend-dist\main.exe
echo.
pause
