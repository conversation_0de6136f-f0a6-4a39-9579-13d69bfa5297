{"name": "clonie-desktop", "version": "1.0.0", "description": "High-Fidelity Voice Cloning Desktop Application", "main": "src/main.js", "author": "Clonie Team", "license": "MIT", "homepage": "./", "scripts": {"start": "electron .", "dev": "electron .", "build": "npm run build-frontend && npm run build-backend && electron-builder", "build-frontend": "cd ../frontend && npm run build && if exist ..\\desktop\\dist rmdir /s /q ..\\desktop\\dist && xcopy /E /I /Y dist ..\\desktop\\dist", "build-backend": "cd ../backend && pyinstaller --onefile --distpath ../desktop/backend-dist main.py", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "installer": "electron-builder --win --x64 --publish=never", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.clonie.desktop", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "release"}, "files": ["src/**/*", "dist/**/*", "backend-dist/**/*", "assets/**/*", "node_modules/**/*"], "extraResources": [{"from": "backend-dist/", "to": "backend/"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON><PERSON><PERSON>", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": true, "runAfterFinish": true, "menuCategory": "Audio & Video"}}, "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1"}, "dependencies": {"axios": "^1.11.0", "electron-is-dev": "^2.0.0", "find-free-port": "^2.0.0", "tree-kill": "^1.2.2"}}