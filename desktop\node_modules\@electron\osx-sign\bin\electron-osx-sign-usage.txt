
NAME
  electron-osx-sign -- code signing for Electron apps

SYNOPSIS
  electron-osx-sign app [embedded-binary ...] [options ...]

DESCRIPTION
  app
    Path to the application package.
    Needs file extension ``.app''.

  embedded-binary ...
    Path to additional binaries that will be signed along with built-ins of Electron, spaced.

  --help
    <PERSON> to display all commands.

  --identity=identity
    Name of certificate to use when signing.
    Default to selected with respect to --provisioning-profile and --platform from --keychain specified or keychain by system default.

  --identityValidation, --no-identityValidation
    Flag to enable/disable validation for the signing identity.

  --ignore=path
    Path to skip signing. The string will be treated as a regular expression when used to match the file paths.

  --keychain=keychain
    The keychain name.
    Default to system default keychain.

  --platform=platform
    Build platform of Electron.
    Allowed values: ``darwin'', ``mas''.
    Default to auto detect from application bundle.

  --pre-auto-entitlements, --no-pre-auto-entitlements
    Flag to enable/disable automation of entitlements file and Info.plist.

  --pre-embed-provisioning-profile, --no-pre-embed-provisioning-profile
    Flag to enable/disable embedding of provisioning profile.

  --provisioning-profile=file
    Path to provisioning profile.

  --strictVerify, --strictVerify=options, --no-strictVerify
    Flag to enable/disable ``--strict'' flag when verifying the signed application bundle.
    Each component should be separated in ``options'' with comma (``,'').
    Enabled by default.

  --type=type
    Specify whether to sign app for development or for distribution.
    Allowed values: ``development'', ``distribution''.
    Default to ``distribution''.

  --version=version
    Build version of Electron.
    Values may be: ``1.2.0''.
    Default to latest Electron version.
