const { app, BrowserWindow, Menu, shell, dialog, ipcMain, Tray, nativeImage } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');
const axios = require('axios');
const findFreePort = require('find-free-port');
const kill = require('tree-kill');
const fs = require('fs');

let mainWindow;
let backendProcess;
let backendPort = 8000;
let tray = null;

// Keep a global reference of the window object
function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false, // Don't show until ready
    titleBarStyle: 'default'
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Load the app
  if (isDev) {
    // Development mode - load from Vite dev server
    mainWindow.loadURL('http://localhost:5173');
  } else {
    // Production mode - load from built files
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Enable drag and drop for audio files
  mainWindow.webContents.on('dom-ready', () => {
    mainWindow.webContents.executeJavaScript(`
      // Prevent default drag behaviors
      document.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
      });

      document.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const files = Array.from(e.dataTransfer.files);
        const audioFiles = files.filter(file => file.type.startsWith('audio/'));

        if (audioFiles.length > 0) {
          // Dispatch custom event for the React app to handle
          window.dispatchEvent(new CustomEvent('electron-file-drop', {
            detail: { files: audioFiles }
          }));
        }
      });
    `);
  });
}

// Start the Python backend server
async function startBackend() {
  try {
    // Find a free port
    const [freePort] = await findFreePort(8000, 8100);
    backendPort = freePort;

    console.log(`Starting backend on port ${backendPort}...`);

    let backendPath;
    if (isDev) {
      // Development mode - run Python script directly
      backendPath = path.join(__dirname, '../../backend/main.py');
      backendProcess = spawn('python', [backendPath], {
        env: { ...process.env, PORT: backendPort.toString() },
        stdio: ['pipe', 'pipe', 'pipe']
      });
    } else {
      // Production mode - run bundled executable
      backendPath = path.join(process.resourcesPath, 'backend', 'main.exe');
      backendProcess = spawn(backendPath, [], {
        env: { ...process.env, PORT: backendPort.toString() },
        stdio: ['pipe', 'pipe', 'pipe']
      });
    }

    backendProcess.stdout.on('data', (data) => {
      console.log(`Backend: ${data}`);
    });

    backendProcess.stderr.on('data', (data) => {
      console.error(`Backend Error: ${data}`);
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
    });

    // Wait for backend to be ready
    await waitForBackend();
    console.log('Backend is ready!');

  } catch (error) {
    console.error('Failed to start backend:', error);
    dialog.showErrorBox('Backend Error', 'Failed to start the voice cloning backend. Please try restarting the application.');
  }
}

// Wait for backend to be responsive
async function waitForBackend(maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      await axios.get(`http://localhost:${backendPort}/health`, { timeout: 2000 });
      return true;
    } catch (error) {
      console.log(`Waiting for backend... (${i + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  throw new Error('Backend failed to start within timeout period');
}

// Stop the backend process
function stopBackend() {
  if (backendProcess) {
    console.log('Stopping backend process...');
    kill(backendProcess.pid, 'SIGTERM', (err) => {
      if (err) {
        console.error('Error stopping backend:', err);
      } else {
        console.log('Backend stopped successfully');
      }
    });
    backendProcess = null;
  }
}

// Create system tray
function createTray() {
  // Create tray icon (use default for now)
  const trayIcon = nativeImage.createFromPath(path.join(__dirname, '../assets/icon.png'));
  tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show Clonie',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        }
      }
    },
    {
      label: 'Hide Clonie',
      click: () => {
        if (mainWindow) {
          mainWindow.hide();
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Voice Cloning Status',
      enabled: false
    },
    {
      label: backendProcess ? '🟢 Backend Running' : '🔴 Backend Stopped',
      enabled: false
    },
    { type: 'separator' },
    {
      label: 'Quit Clonie',
      click: () => {
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('Clonie - High-Fidelity Voice Cloning');

  // Double click to show/hide window
  tray.on('double-click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    }
  });
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Open Audio File',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-open-file');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Clonie',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Clonie',
              message: 'Clonie v1.0.0',
              detail: 'High-Fidelity Voice Cloning Desktop Application\n\nPowered by XTTS v2 • Built with Electron & FastAPI'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  console.log('Electron app is ready');
  
  // Start backend first
  await startBackend();
  
  // Create window, menu, and tray
  createWindow();
  createMenu();
  createTray();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  stopBackend();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  stopBackend();
});

// IPC handlers
ipcMain.handle('get-backend-port', () => {
  return backendPort;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.hide(); // Hide instead of close to keep in tray
  }
});

ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

console.log('Clonie Desktop starting...');
