#!/bin/bash

echo ""
echo "╔═══════════════════════════════════════════════════════════════╗"
echo "║                    Starting Clonie Web App                   ║"
echo "╠═══════════════════════════════════════════════════════════════╣"
echo "║   🎙️  High-Fidelity Voice Cloning Studio                    ║"
echo "║   🚀  React Frontend + Python FastAPI Backend               ║"
echo "╚═══════════════════════════════════════════════════════════════╝"
echo ""

echo "📦 Installing backend dependencies..."
cd backend
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

echo ""
echo "📦 Installing frontend dependencies..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

echo ""
echo "🚀 Starting backend server..."
cd ../backend
python main.py &
BACKEND_PID=$!

echo ""
echo "⏳ Waiting for backend to initialize..."
sleep 5

echo ""
echo "🌐 Starting frontend development server..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Clonie is running!"
echo ""
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:8000"
echo ""
echo "💡 Press Ctrl+C to stop both servers"
echo ""

# Wait for user interrupt
trap "echo ''; echo '🛑 Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo '👋 Clonie stopped.'; exit 0" INT

# Keep script running
wait
