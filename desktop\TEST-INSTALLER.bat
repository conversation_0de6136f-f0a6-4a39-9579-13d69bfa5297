@echo off
title Test Clonie Installer

echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                   🧪 TEST CLONIE INSTALLER 🧪                ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 Looking for installer...

if exist "release\Clonie-Setup-*.exe" (
    for %%f in (release\Clonie-Setup-*.exe) do (
        echo ✅ Found installer: %%~nxf
        echo 📍 Location: %cd%\release\
        echo 📏 Size: %%~zf bytes
        echo.
        
        echo 🚀 What would you like to do?
        echo.
        echo [1] Run the installer (install C<PERSON>ie)
        echo [2] Open the release folder
        echo [3] Exit
        echo.
        set /p choice="Enter your choice (1-3): "
        
        if "!choice!"=="1" (
            echo.
            echo 🎯 Launching installer...
            echo    Follow the installation wizard to install <PERSON>lonie
            echo    After installation, you can launch <PERSON><PERSON><PERSON> from:
            echo    • Start Menu → Clonie
            echo    • Desktop shortcut (if created)
            echo.
            start "" "%%f"
            echo ✅ Installer launched!
        ) else if "!choice!"=="2" (
            echo.
            echo 📁 Opening release folder...
            explorer release
        ) else (
            echo.
            echo 👋 Exiting...
        )
    )
) else (
    echo ❌ No installer found!
    echo.
    echo 🔧 To create an installer:
    echo    1. Run BUILD-INSTALLER.bat
    echo    2. Wait for the build to complete
    echo    3. Then run this script again
    echo.
    
    set /p build="Build installer now? (Y/N): "
    if /i "!build!"=="Y" (
        echo.
        echo 🚀 Starting build process...
        call BUILD-INSTALLER.bat
    )
)

echo.
pause
