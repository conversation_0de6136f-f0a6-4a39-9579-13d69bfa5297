# Assets Directory

This directory contains application assets for the Electron desktop app.

## Required Files

- `icon.png` - Application icon (256x256 PNG)
- `icon.ico` - Windows icon file (for installer)

## Icon Requirements

The application icon should be:
- 256x256 pixels minimum
- PNG format for cross-platform compatibility
- ICO format for Windows installer
- Represents the Clonie brand (microphone/voice theme)

## Generating Icons

You can use online tools or ImageMagick to convert PNG to ICO:
```bash
convert icon.png -define icon:auto-resize=256,128,64,48,32,16 icon.ico
```

For now, the app will use a default Electron icon until custom icons are added.
