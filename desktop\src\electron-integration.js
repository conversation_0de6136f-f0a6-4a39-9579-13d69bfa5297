// Electron integration script - injected into the web app
// This adds desktop-specific features to the existing web application

(function() {
    'use strict';
    
    console.log('🖥️ Clonie Desktop Integration Loading...');
    
    // Check if we're running in Electron
    const isElectron = window.electronAPI && window.electronAPI.isElectron;
    
    if (!isElectron) {
        console.log('Not running in Electron, skipping desktop integration');
        return;
    }
    
    console.log('✅ Running in Electron - enabling desktop features');
    
    // Initialize desktop features when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDesktopFeatures);
    } else {
        initDesktopFeatures();
    }
    
    async function initDesktopFeatures() {
        try {
            // Get the backend port from Electron
            const backendPort = await window.electronAPI.getBackendPort();
            console.log(`🔌 Backend running on port: ${backendPort}`);
            
            // Update API base URL if needed
            if (window.VoiceCloningAPI) {
                // Update the API base URL to use the dynamic port
                const newBaseURL = `http://localhost:${backendPort}`;
                console.log(`🔄 Updating API base URL to: ${newBaseURL}`);
                
                // This would need to be implemented in the API service
                // For now, we'll just log it
            }
            
            // Add desktop indicator to the header
            addDesktopIndicator();
            
            // Enable drag and drop for audio files
            enableDragAndDrop();
            
            // Add desktop-specific keyboard shortcuts
            addKeyboardShortcuts();
            
            // Add window controls if needed
            addWindowControls();
            
            console.log('✅ Desktop features initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize desktop features:', error);
        }
    }
    
    function addDesktopIndicator() {
        // Find the header and add a desktop indicator
        const header = document.querySelector('h1');
        if (header && !document.querySelector('.desktop-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'desktop-indicator';
            indicator.innerHTML = `
                <div style="
                    display: inline-flex;
                    align-items: center;
                    background: rgba(59, 130, 246, 0.1);
                    border: 1px solid rgba(59, 130, 246, 0.2);
                    border-radius: 9999px;
                    padding: 4px 12px;
                    margin-left: 12px;
                    font-size: 14px;
                    font-weight: 500;
                    color: rgba(59, 130, 246, 1);
                ">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 4px;">
                        <path d="M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z"/>
                    </svg>
                    Desktop
                </div>
            `;
            header.parentNode.insertBefore(indicator, header.nextSibling);
        }
    }
    
    function enableDragAndDrop() {
        // Listen for file drop events from Electron
        if (window.electronAPI.onFileDrop) {
            window.electronAPI.onFileDrop((event) => {
                const files = event.detail.files;
                if (files && files.length > 0) {
                    console.log('📁 Files dropped:', files);
                    
                    // Trigger the file upload in the web app
                    // This would need to integrate with the existing file upload logic
                    const fileInput = document.querySelector('input[type="file"]');
                    if (fileInput) {
                        // Create a new FileList with the dropped files
                        const dataTransfer = new DataTransfer();
                        files.forEach(file => dataTransfer.items.add(file));
                        fileInput.files = dataTransfer.files;
                        
                        // Trigger change event
                        fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            });
        }
    }
    
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+O - Open file
            if (event.ctrlKey && event.key === 'o') {
                event.preventDefault();
                const fileInput = document.querySelector('input[type="file"]');
                if (fileInput) {
                    fileInput.click();
                }
            }
            
            // Ctrl+Q - Quit (handled by Electron)
            if (event.ctrlKey && event.key === 'q') {
                event.preventDefault();
                // Electron will handle this
            }
        });
    }
    
    function addWindowControls() {
        // Add minimize/maximize/close buttons if needed
        // For now, we'll rely on the native window controls
        console.log('🪟 Using native window controls');
    }
    
    // Enhanced download function for desktop
    window.desktopDownload = async function(audioId, filename) {
        if (window.electronAPI && window.electronAPI.showSaveDialog) {
            try {
                const result = await window.electronAPI.showSaveDialog({
                    title: 'Save Audio File',
                    defaultPath: filename,
                    filters: [
                        { name: 'Audio Files', extensions: ['wav'] },
                        { name: 'All Files', extensions: ['*'] }
                    ]
                });
                
                if (!result.canceled && result.filePath) {
                    // Download to the selected location
                    const url = `http://localhost:${await window.electronAPI.getBackendPort()}/audio/${audioId}`;
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = result.filePath.split(/[\\/]/).pop() || filename;
                    link.click();
                    
                    return result.filePath;
                }
            } catch (error) {
                console.error('Desktop download failed:', error);
            }
        }
        
        // Fallback to regular download
        return null;
    };
    
    // Update page title for desktop
    document.title = 'Clonie Desktop - High-Fidelity Voice Cloning';
    
    console.log('🎉 Clonie Desktop Integration Complete!');
})();
