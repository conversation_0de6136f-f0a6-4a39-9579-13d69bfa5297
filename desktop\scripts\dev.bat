@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║            Starting Clonie Desktop in Development Mode       ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0.."

echo 📦 Installing dependencies...
call npm install

echo.
echo 🔧 Building frontend for development...
cd ..\frontend
call npm install
call npm run build
cd ..\desktop

echo.
echo 📁 Copying frontend build...
if not exist "dist" mkdir "dist"
xcopy /E /I /Y "..\frontend\dist\*" "dist\"

echo.
echo 🚀 Starting Electron in development mode...
echo    - Backend will run from Python source
echo    - Frontend will load from built files
echo    - Hot reload disabled for stability
echo.

call npm start

pause
