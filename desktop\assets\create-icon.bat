@echo off
echo.
echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                   🎨 SETTING UP CLONIE ICONS 🎨              ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 Checking for custom icon...

if exist "clonie_icon.png" (
    echo ✅ Found clonie_icon.png

    echo.
    echo 📋 Setting up icon files for Electron app...

    :: Copy the custom icon as the main icon
    copy "clonie_icon.png" "icon.png" >nul
    echo ✅ Created icon.png (main application icon)

    :: Check if we have ImageMagick or other tools to convert to ICO
    echo.
    echo 🔄 Converting PNG to ICO format...

    :: Try using PowerShell to convert (Windows 10+)
    powershell -Command "& {
        try {
            Add-Type -AssemblyName System.Drawing
            $png = [System.Drawing.Image]::FromFile('%cd%\clonie_icon.png')
            $ico = New-Object System.Drawing.Icon($png, 256, 256)
            $fileStream = [System.IO.File]::Create('%cd%\icon.ico')
            $ico.Save($fileStream)
            $fileStream.Close()
            $ico.Dispose()
            $png.Dispose()
            Write-Host '✅ Successfully created icon.ico'
        } catch {
            Write-Host '⚠️  PowerShell conversion failed, using online converter recommendation'
        }
    }"

    if exist "icon.ico" (
        echo ✅ ICO file created successfully!
    ) else (
        echo.
        echo ⚠️  ICO conversion failed. You have two options:
        echo.
        echo    Option 1 - Online Converter (Recommended):
        echo    1. Go to https://convertio.co/png-ico/
        echo    2. Upload your clonie_icon.png
        echo    3. Download the .ico file
        echo    4. Save it as 'icon.ico' in this folder
        echo.
        echo    Option 2 - Use ImageMagick:
        echo    1. Install ImageMagick from https://imagemagick.org/
        echo    2. Run: magick clonie_icon.png icon.ico
        echo.
        echo    For now, I'll create a copy as placeholder...
        copy "clonie_icon.png" "icon.ico" >nul
        echo ✅ Created placeholder icon.ico (may not work perfectly)
    )

    echo.
    echo 📝 Icon files created:
    if exist "icon.png" echo    ✅ icon.png (256x256 recommended)
    if exist "icon.ico" echo    ✅ icon.ico (Windows format)

    echo.
    echo 🎯 Your custom Clonie icon is now set up!
    echo    The installer and application will use your custom icon.

) else (
    echo ❌ clonie_icon.png not found!
    echo.
    echo 📁 Please make sure you have:
    echo    • clonie_icon.png in the desktop/assets/ folder
    echo    • Icon should be 256x256 pixels (recommended)
    echo    • PNG format with transparent background (optional)
    echo.
    echo 💡 Once you add the icon file, run this script again.
)

echo.
echo 🎨 Icon setup complete!
echo    Your Clonie desktop app will now use the custom icon for:
echo    • Application window
echo    • System tray
echo    • Desktop shortcut
echo    • Start Menu entry
echo    • Windows installer
echo.

pause
